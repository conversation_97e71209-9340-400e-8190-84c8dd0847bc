<!doctype html>
<html ⚡ lang="en">

<head>
    <!-- Essential Meta Tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1">
    <meta name="robots" content="index, follow, archive">

    <!-- Page Title -->
    <title>Solved: What Scales To Play Over 12 Bar Blues</title>

    <!-- Primary Meta Tags -->
    <meta name="description"
        content="Learn how to solo over 12 bar blues, and how to switch to the right scales, at the right place on the guitar fretboard.">

    <!-- Open Graph Meta Tags -->
    <meta property="og:url" content="https://justplay.systems/test-page">
    <meta property="og:site_name" content="Effective Music Practice">
    <meta property="og:title" content="Solved: What Scales To Play Over 12 Bar Blues">
    <meta property="og:description"
        content="Learn how to solo over 12 bar blues, and how to switch to the right scales, at the right place on the guitar fretboard.">
    <meta property="og:image"
        content="https://effectivemusicpractice.b-cdn.net/assets/images/share/what-scales-to-play-over-12-bar-blues-1200px-1701768237300.webp">
    <meta property="og:image:alt" content="Solved: What Scales To Play Over 12 Bar Blues">

    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Solved: What Scales To Play Over 12 Bar Blues">
    <meta name="twitter:description"
        content="Learn how to solo over 12 bar blues, and how to switch to the right scales, at the right place on the guitar fretboard.">
    <meta name="twitter:image"
        content="https://effectivemusicpractice.b-cdn.net/assets/images/share/what-scales-to-play-over-12-bar-blues-1200px-1701768237300.webp">

    <!-- AMP Required Scripts -->
    <script async src="https://cdn.ampproject.org/v0.js"></script>
    <script async custom-element="amp-bind" src="https://cdn.ampproject.org/v0/amp-bind-0.1.js"></script>
    <script async custom-element="amp-sidebar" src="https://cdn.ampproject.org/v0/amp-sidebar-0.1.js"></script>
    <script async custom-element="amp-social-share"
        src="https://cdn.ampproject.org/v0/amp-social-share-0.1.js"></script>
    <script async custom-element="amp-youtube" src="https://cdn.ampproject.org/v0/amp-youtube-0.1.js"></script>

    <!-- Canonical Link -->
    <link rel="canonical" href="https://justplay.systems/test-page">

    <!-- Icons -->
    <link rel="icon" type="image/webp" sizes="16x16" href="/assets/icons/icon-16px.webp">
    <link rel="icon" type="image/webp" sizes="32x32" href="/assets/icons/icon-32px.webp">
    <link rel="icon" type="image/webp" sizes="192x192" href="/assets/icons/icon-192px.webp">
    <link rel="apple-touch-icon" sizes="87x87" href="/assets/icons/icon-87px.png" type="image/png">
    <link rel="apple-touch-icon" sizes="120x120" href="/assets/icons/icon-120px.png" type="image/png">
    <link rel="apple-touch-icon" sizes="152x152" href="/assets/icons/icon-152px.png" type="image/png">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/icons/icon-180px.png" type="image/png">

    <!-- Required AMP boilerplate -->
    <style amp-boilerplate>
        body {
            -webkit-animation: -amp-start 8s steps(1, end) 0s 1 normal both;
            -moz-animation: -amp-start 8s steps(1, end) 0s 1 normal both;
            -ms-animation: -amp-start 8s steps(1, end) 0s 1 normal both;
            animation: -amp-start 8s steps(1, end) 0s 1 normal both
        }

        @-webkit-keyframes -amp-start {
            from {
                visibility: hidden
            }

            to {
                visibility: visible
            }
        }

        @-moz-keyframes -amp-start {
            from {
                visibility: hidden
            }

            to {
                visibility: visible
            }
        }

        @-ms-keyframes -amp-start {
            from {
                visibility: hidden
            }

            to {
                visibility: visible
            }
        }

        @-o-keyframes -amp-start {
            from {
                visibility: hidden
            }

            to {
                visibility: visible
            }
        }

        @keyframes -amp-start {
            from {
                visibility: hidden
            }

            to {
                visibility: visible
            }
        }
    </style>

    <noscript>
        <style amp-boilerplate>
            body {
                -webkit-animation: none;
                -moz-animation: none;
                -ms-animation: none;
                animation: none
            }
        </style>
    </noscript>

    <!-- Preconnect to Google Fonts servers for faster font loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- For Montserrat font -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">

    <!-- STYLES -->
    <style amp-custom>
        /* ---------------------- GENERAL CSS ---------------------- */
        /* Defaults for Melo Static Pages produced by the Melo Website Builder */

        * {
            margin: 0;
            padding: 0;
        }

        :root {
            --transparentHeaderNav: #242424ed;
            --meloShadow: 0px 0px 10px #202020;
            --fontMontserrat: 'Montserrat', Verdana, sans-serif;
        }

        .dark-mode {
            --c000: #121212;
            --c050: #2D2D2D;
            --c100: #404040;
            --c200: #545454;
            --c300: #6C6C6C;
            --c400: #828282;
            --c500: #9C9C9C;
            --c600: #AAAAAA;
            --c700: #C8C8C8;
            --c800: #EBEBEB;
            --c900: #F7F7F7;
            --c950: #FFFFFF;
            --softBackground: #2d2d2d2c;
            --whiteBlack: #000;
            --projectAccent: #f9a82f;
            --projectSecondary: #ffffff;
            --transparentHeaderNav: #242424ed;
            --meloShadow: 0px 0px 10px #202020;
            /* Optional styles for non-transparent or sticker-like logos */
            /* user specifies logo background color */
            /* --logoBackgroundColor: #000000; */
        }

        .light-mode {
            --c000: #FFFFFF;
            --c050: #F7F7F7;
            --c100: #EBEBEB;
            --c200: #C8C8C8;
            --c300: #AAAAAA;
            --c400: #9C9C9C;
            --c500: #828282;
            --c600: #6C6C6C;
            --c700: #545454;
            --c800: #404040;
            --c900: #2D2D2D;
            --c950: #121212;
            --softBackground: #f7f7f7;
            --whiteBlack: #fff;
            --projectAccent: #f9a82f;
            --projectSecondary: #524d88;
            --transparentHeaderNav: #ffffffed;
            --meloShadow: 0px 0px 10px #d8d8d8;
            /* Optional styles for non-transparent or sticker-like logos */
            /* user specifies logo background color */
            /* --logoBackgroundColor: #ffffff; */
        }

        body {
            background-color: var(--c000);
            color: var(--c900);
            line-height: 1.7rem;
        }

        html {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        html::-webkit-scrollbar {
            display: none;
        }

        amp-state-container {
            height: 0;
            width: 0;
            position: absolute;
            visibility: hidden;
            margin: 0;
            padding: 0;
        }

        header {
            position: sticky;
            top: 0;
            left: 0;
            right: 0;
            z-index: 12;
        }

        /* ---------------------- MELO CSS ---------------------- */
        /* Needed for all/most melo-blocks. Add to the end of the <head> in other websites */

        @font-face {
            font-family: "Montserrat-fallback";
            size-adjust: 102%;
            src: local("Verdana");
        }

        html {
            scroll-behavior: smooth;
            --meloShadow: 0px 0px 10px #d8d8d8;
            --fontMontserrat: 'Montserrat', 'Montserrat-fallback', Verdana, sans-serif;
        }

        .melo,
        .melo * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .melo-container-wrapper {
            container-type: inline-size;
        }

        .melo-width-boxed {
            max-width: 870px;
            margin: auto;
        }

        .melo-scroll-container::-webkit-scrollbar {
            display: none;
        }

        .melo-scroll-container {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .melo-two-line-ellipsis {
            display: -webkit-box;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            max-height: 2.5em;
            line-height: 1.25;
            width: 100%;
            word-break: break-word;
        }

        @media (max-width:953px) {
            .melo-width-boxed {
                max-width: 90vw;
            }
        }

        /* ---------------------- NAV BAR & FOOTER STYLES ---------------------- */
        /* Only for Melo Static Pages produced by the Melo Website Builder. */

        /* Navigation Menu START */

        .melo-header-nav-wrapper {
            font-size: 14px;
            line-height: 0rem;
            top: 0;
            left: 0;
            right: 0;
            z-index: 13;
            background-color: var(--transparentHeaderNav);
            height: 64px;
            border-bottom: 1px solid var(--c200);
            container-type: inline-size;
            transform: translateZ(0);
            /* Force hardware acceleration */
            -webkit-transform: translateZ(0);
            will-change: transform;
            /* Optimize for animations */
        }

        .melo-header-nav {
            display: flex;
            height: 100%;
            max-width: 90%;
            margin: auto;
            padding: 8px 0;
            color: var(--c700);
            font-family: var(--fontMontserrat);
            align-items: center;
        }

        .melo-hamburger-menu-wrapper {
            width: auto;
            display: none;
            position: relative;
            z-index: 13;
            transition: ease 0.3s;
        }

        .melo-hamburger-menu-wrapper:hover {
            transform: scale(1.1);
        }

        .melo-hamburger-menu {
            margin: auto 8px auto 0;
            background: none;
            border: none;
            cursor: pointer;
            padding: 0;
            transition: transform 0.3s ease-in;
        }

        .melo-hamburger-menu .hamburger-bar {
            background-color: var(--c700);
            display: block;
            width: 30px;
            height: 3px;
            margin: 6px 0;
            transition: 0.3s;
            position: relative;
        }

        /* X icon styles when menu is open */
        .melo-hamburger-menu.open .hamburger-bar:nth-child(1) {
            transform: rotate(45deg) translate(6px, 6px);
        }

        .melo-hamburger-menu.open .hamburger-bar:nth-child(2) {
            opacity: 0;
        }

        .melo-hamburger-menu.open .hamburger-bar:nth-child(3) {
            transform: rotate(-45deg) translate(6px, -6px);
        }

        amp-sidebar {
            width: 360px;
            max-width: none;
            /* Add this */
            background-color: var(--c100);
            padding: 32px;
            height: calc(100vh - 64px);
            top: 64px;
            /* Match header height */
            max-height: none;
            box-shadow: var(--meloShadow);
            z-index: 15;
        }

        /* Use media query instead of container query */
        @media screen and (max-width: 500px) {
            amp-sidebar {
                width: 100%;
                max-width: 100%;
            }
        }

        amp-sidebar#melo-mobile-menu ul {
            width: 100%;
            padding-right: 0;
            /* Reset any potential padding */
        }

        amp-sidebar#melo-mobile-menu ul li {
            list-style: none;
            text-decoration: none;
            font-family: var(--fontMontserrat);
            font-size: 1rem;
            padding: 1.5rem 1rem 1.5rem 1rem;
            /* Explicitly set padding on all sides */
            border-bottom: 1px solid var(--c300);
            transition: ease 0.3s;
            box-sizing: border-box;
            /* Ensure padding is included in width calculation */
        }

        amp-sidebar#melo-mobile-menu ul li:hover {
            background-color: var(--c200);
            transition: ease 0.3s;
            cursor: pointer;
        }

        amp-sidebar#melo-mobile-menu ul li a {
            color: var(--c700);
            text-decoration: none;
            padding: 0 8px 0 0;
            /* Add right padding to ensure text doesn't touch the edge */
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            width: calc(100% - 8px);
            /* Adjust width to account for right padding */
            line-height: 1.25;
            box-sizing: border-box;
            /* Ensure padding is included in width calculation */
        }

        amp-sidebar#melo-mobile-menu {
            width: 360px;
            background-color: var(--c100);
            padding: 32px 32px 32px 32px;
            /* Explicitly set padding on all sides */
            height: calc(100vh - 64px);
            top: 64px;
            max-height: none;
            box-shadow: var(--meloShadow);
            z-index: 10;
        }

        @media screen and (max-width: 500px) {
            amp-sidebar#melo-mobile-menu {
                width: 100%;
                padding: 32px;
                /* Force padding on all sides in mobile view */
            }
        }

        /* Dark mode toggle in mobile menu */
        .melo-dark-mode-div-mobile {
            display: flex;
            align-items: center;
            padding: 0;
            /* Remove padding since it's in the li */
        }

        .melo-dark-mode-div-mobile p {
            margin: 0 0 0 10px;
            color: var(--c700);
        }

        [class*="amphtml-sidebar-mask"] {
            z-index: 5;
        }

        #logoAnchor {
            height: 100%;
            width: auto;
            flex-grow: 0;
            overflow: hidden;
            transition: transform 0.3s ease-in;
            margin: auto;

            /* OPTIONAL START */
            /* Optional styles for non-transparent or sticker-like logos */

            /*
            display: flex;
            border-radius: 4px;
            padding: 4px 8px;
            width: 196px;
            box-shadow: var(--meloShadow);
            background-color: var(--logoBackgroundColor);
            justify-content: center;
            align-items: center;
            */

            /* OPTIONAL END */
        }

        #logoAnchor:hover {
            transform: scale(1.04);
        }

        amp-img#logo {
            width: 216px;
            /* Fixed width to prevent shrinking */
            height: 48px;
            /* Fixed height to match the aspect ratio */
            min-width: 216px;
            /* Ensure logo doesn't shrink below this width */
            max-width: none;
            /* Override max-width constraint */
            object-fit: contain;
            /* Ensure the image maintains its aspect ratio within the fixed dimensions */
        }

        a#logoAnchor {
            min-width: 216px;
            flex-shrink: 0;
            /* Prevent the anchor from shrinking */
        }

        .melo-header-nav ul {
            display: flex;
            flex-direction: row;
            flex-grow: 1;
            /* Allow the ul to grow */
            flex-shrink: 1;
            /* Allow the ul to shrink */
            justify-content: flex-end;
            align-items: center;
            margin: auto 8px auto auto;
            column-gap: 4px;
            height: 100%;
            width: auto;
            overflow: hidden;
            /* Hide overflowing items */
        }

        .melo-header-nav ul li {
            display: flex;
            list-style: none;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            padding: 4px 16px;
            border-radius: 4px;
            /* change max-width to percentage points 100 divided by number of menu items */
            max-width: 33%;
            height: 48px;
            align-items: center;
        }

        .melo-header-nav li.melo-header-nav-highlighted-link {
            background-color: var(--c050);
            border-bottom: 3px solid var(--c200);
        }

        .melo-header-nav ul li:hover {
            border-bottom: 3px solid var(--projectAccent);
            transform: scaleX(1.02);
            cursor: pointer;
        }

        .melo-header-nav ul li a,
        .melo-header-nav ul li a:visited {
            color: var(--c700);
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            padding: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: normal;
        }

        .melo-dark-mode-div-wrapper {
            margin: 0 0 0 8px;
            /* Keep only left margin, remove auto margins */
            height: 100%;
            width: auto;
            padding: 0;
            /* Ensure no padding */
        }

        .melo-dark-mode-div {
            font-size: 8px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin: 0;
            /* Remove auto margin */
            height: 100%;
            width: auto;
            padding: 0;
            /* Ensure no padding */
        }

        .melo-dark-mode-div,
        .melo-dark-mode-div-mobile {
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: auto;
            height: 100%;
            width: auto;
        }

        .melo-dark-mode-div-mobile {
            font-size: 12px;
            flex-direction: row;
            column-gap: 8px;
        }

        .melo-dark-mode-div p {
            line-height: 0;
            margin: 8px 0 0 0;
            text-wrap: nowrap;
        }

        .dark-mode-toggle-switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 18px;
            transition: transform 0.3s ease-in;
        }

        .dark-mode-toggle-switch:hover {
            transform: scale(1.1);
        }

        .dark-mode-toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .-dark-mode-toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--c300);
            transition: .4s;
        }

        .-dark-mode-toggle-slider:before {
            position: absolute;
            content: "";
            height: 10px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: #ffffff;
            transition: .4s;
            border-radius: 4px;
        }

        input+.-dark-mode-toggle-slider {
            border-radius: 4px;
        }

        input:checked+.-dark-mode-toggle-slider:before {
            transform: translateX(16px);
        }

        .melo-share-icon {
            display: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .melo-share-icon:hover {
            transform: scale(1.1);
        }

        #dark-mode-li {
            display: none;
        }

        @media (max-width: 960px) {
            .melo-header-nav ul {
                display: none;
            }

            .melo-hamburger-menu-wrapper {
                display: flex;
            }

            .melo-dark-mode-div-wrapper {
                display: none;
            }

            #dark-mode-li {
                display: flex;
            }

            .melo-share-icon {
                display: block;
            }
        }

        @media (max-width: 500px) {
            .melo-mobile-menu {
                width: 100%;
            }
        }

        /* Navigation Menu END */

        /* Footer START */

        #melo-footer {
            background-color: var(--c100);
            color: var(--c700);
            font-family: var(--fontMontserrat);
            font-size: 0.9rem;
            line-height: 2.2;
            text-align: center;
        }

        #melo-footer a {
            text-decoration: none;
            color: var(--c800);
            font-weight: 600;
            transition: ease 0.3s;
        }

        #melo-footer a:hover {
            border-bottom: 3px solid var(--projectAccent);
            cursor: pointer;
        }

        #melo-footer .melo-footer-wrapper {
            -webkit-column-count: 3;
            -moz-column-count: 3;
            column-count: 3;
            padding: 60px 20px 20px 20px;
            column-gap: 30px;
        }

        #melo-footer .melo-footer-bottom-centered {
            font-size: 0.8rem;
            margin: auto;
            padding: 20px 0 60px;
            width: 500px;
            text-align: center;
        }

        @media (max-width: 900px) {
            #melo-footer .melo-footer-wrapper {
                -webkit-column-count: 3;
                -moz-column-count: 3;
                column-count: 3;
            }
        }

        @media (max-width: 761px) {
            #melo-footer .melo-footer-wrapper {
                -webkit-column-count: 2;
                -moz-column-count: 2;
                column-count: 2;
            }
        }

        @media (max-width: 480px) {
            #melo-footer .melo-footer-wrapper {
                -webkit-column-count: 1;
                -moz-column-count: 1;
                column-count: 1;
            }
        }

        /* Footer END */

        /* ---------------------- MELO BLOCK STYLES ---------------------- */

        /* melo-yt-video */
        .melo-yt-video {
            position: relative;
            overflow: hidden;
            aspect-ratio: 16/9;
        }

        /* melo-columns */
        .melo-columns {
            display: flex;
            flex-direction: row;
        }

        .melo-column {
            width: 100%;
            display: flex;
            flex-direction: column;
        }

        @media (max-width: 700px) {
            .melo-columns {
                flex-direction: column;
            }

            .melo-column {
                min-width: 100%;
                max-width: 100%;
            }
        }

        /* melo-hero-header */
        .melo-hero-header {
            background-attachment: fixed;
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
        }

        .melo-hero-header-overlay {
            container-type: inline-size;
        }

        .melo-hero-header-banner {
            font-family: Verdana, Geneva, Tahoma, sans-serif;
            font-size: clamp(8px, 3vw, 16px);
            border-radius: 5px;
            padding: 2px 18px;
            box-shadow: 1px 1px 8px #80808090;
            margin-bottom: 24px;
            line-height: 1.8;
            width: fit-content;
            max-width: 90%;
        }

        /* Combined text styles for better maintainability */
        .melo-hero-header-main-text,
        .melo-hero-header-secondary-text {
            font-family: var(--fontMontserrat);
            text-shadow: 1px 1px 8px #80808090;
        }

        .melo-hero-header-main-text {
            font-size: clamp(28px, 9vw, 76px);
            line-height: 1.1;
            margin-bottom: 10px;
        }

        .melo-hero-header-secondary-text {
            font-size: clamp(14px, 4.5vw, 38px);
            margin-bottom: 18px;
            line-height: 1.2;
        }

        .melo-hero-header a {
            font-size: clamp(12px, 4vw, 24px);
            line-height: 1.8rem;
            font-family: Verdana, Geneva, Tahoma, sans-serif;
            font-weight: bold;
            padding: 0.6em 1.2em;
            box-shadow: 1px 1px 8px #80808090;
            border-radius: 8px;
            margin-top: 24px;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            max-width: 90%;
        }

        .melo-hero-header a:hover {
            transform: scale(1.05);
            cursor: pointer;
        }

        /* melo-dates */
        .melo-dates-inner {
            font-family: "Courier New", Courier, "Lucida Console", Monaco, monospace;
            background-color: var(--softBackground);
            border-left: 3px solid;
            padding: 12px 20px;
            border-radius: 0 4px 4px 0;
            letter-spacing: -0.5px;
        }

        .melo-dates-inner p {
            margin: 0;
            line-height: 1.5;
            font-size: 0.9rem;
        }

        .melo-dates-inner time,
        .melo-dates-inner .author {
            font-weight: 950;
        }

        .melo-dates-inner .author a {
            color: var(--c600);
            text-decoration: none;
            transition: .3s;
        }

        .melo-dates-inner .author a:hover {
            border-bottom: 3px solid var(--projectAccent);
        }

        /* melo-breadcrumb */
        .melo-breadcrumb {
            font-family: var(--fontMontserrat);
            padding: 12px 0;
            color: var(--c700);
            font-size: 0.8rem;
        }

        .melo-breadcrumb ul {
            list-style-type: none;
            margin: 0;
            padding: 0;
            display: flex;
            flex-wrap: wrap;
        }

        .melo-breadcrumb ul li {
            display: inline-block;
            margin: 0;
            line-height: 1.5;
        }

        .melo-breadcrumb ul li:not(:last-child)::after {
            content: "/";
            margin: 0 8px;
            color: var(--c400);
        }

        .melo-breadcrumb a,
        .melo-breadcrumb a:visited,
        .melo-breadcrumb a:active {
            color: var(--c800);
            text-decoration: none;
            transition: ease 0.3s;
            font-weight: 600;
        }

        .melo-breadcrumb a:hover {
            border-bottom: 3px solid var(--projectAccent);
        }

        .melo-breadcrumb span {
            color: var(--c500);
        }

        /* melo-toc */
        .melo-toc {
            font-family: "Courier New", Courier, "Lucida Console", Monaco, monospace;
            background-color: var(--softBackground);
            border-left: 3px solid;
            padding: 12px 20px;
            border-radius: 0 4px 4px 0;
            letter-spacing: -0.5px;
            color: var(--c900);
        }

        .melo-toc ul {
            list-style-type: none;
            margin: 0;
            padding: 0;
        }

        .melo-toc ul ul {
            padding-left: 20px;
            padding-top: 3px;
            padding-bottom: 3px;
        }

        .melo-toc li {
            margin: 0;
            line-height: 1.3;
        }

        .melo-toc a,
        .melo-toc a:visited,
        .melo-toc a:active {
            color: var(--c900);
            text-decoration: none;
            transition: ease 0.3s;
            font-size: 0.9rem;
        }

        /* Style section headers */
        .melo-toc .section-header {
            font-weight: 950;
            margin: 0;
            font-size: 0.9rem;
            color: var(--c600);
        }

        .melo-toc a:hover {
            border-bottom: 3px solid var(--projectAccent);
        }

        /* melo-text */
        .melo-container-wrapper[id^="melo-text"] {
            margin: 32px auto 32px auto;
        }

        .melo-text {
            font-family: var(--fontMontserrat);
            color: var(--c800);
            line-height: 1.7;
        }

        .melo-text h1,
        .melo-text h2,
        .melo-text h3,
        .melo-text h4,
        .melo-text h5,
        .melo-text h6 {
            margin-bottom: 1rem;
            margin-top: 2rem;
            line-height: 1.3;
            color: var(--c500);
            text-shadow: var(--meloShadow);
        }

        .melo-text h1 {
            font-size: 2.5rem;
        }

        .melo-text h2 {
            font-size: 2rem;
        }

        .melo-text h3 {
            font-size: 1.5rem;
        }

        .melo-text p {
            margin-bottom: 1rem;
        }

        .melo-text ul,
        .melo-text ol {
            margin: 1.6rem 0 1.6rem 2.5rem;
        }

        .melo-text li {
            line-height: 0.7;
        }

        .melo-text a,
        .melo-text a:visited,
        .melo-text a:active {
            color: var(--c800);
            text-decoration: underline;
            text-decoration-color: var(--projectAccent);
            text-decoration-thickness: 2px;
            text-underline-offset: 4px;
            transition: ease 0.3s;
            font-weight: 600;
        }

        .melo-text a:hover {
            background-color: var(--c100);
        }

        .melo-text blockquote {
            background-color: var(--softBackground);
            border-left: 3px solid var(--projectAccent);
            padding: 16px;
            border-radius: 0 4px 4px 0;
            margin: 1rem 0;
            font-size: 1.2rem;
            line-height: 1.5;
        }

        .melo-text blockquote p {
            margin: 0;
            color: var(--c500);
        }

        .melo-text pre {
            background-color: var(--c200);
            padding: 16px;
            border-radius: 4px;
            overflow-x: auto;
            margin: 1rem 0;
        }

        .melo-text code {
            font-family: "Courier New", Courier, "Lucida Console", Monaco, monospace;
            font-size: 0.9rem;
        }

        .melo-text strong {
            color: var(--c900);
        }

        /* Text alignment classes */
        .melo-text-align-center {
            text-align: center;
        }

        .melo-text-align-right {
            text-align: right;
        }

        .melo-text-align-justify {
            text-align: justify;
        }

        /* media queries */
        @media (max-width: 991px) {
            .melo-hero-header-banner {
                padding: 0.2em 1em;
                margin-bottom: 2.7vw;
            }

            .melo-hero-header a {
                margin-top: 2.2vw;
            }
        }

        @media (max-width: 767px) {
            .melo-hero-header-banner {
                padding: 0.3em 1em;
            }
        }

        /* melo-button */
        .melo-button {
            display: inline-block;
            transition: ease 0.3s;
            font-family: var(--fontMontserrat);
            box-shadow: var(--meloShadow);
            text-decoration: none;
            cursor: pointer;
            text-align: center;
        }

        .melo-button:hover {
            transform: scale(1.06);
        }

        /* melo-testimonial */
        .melo-testimonial-slider {
            overflow: hidden;
        }

        .melo-testimonial-row {
            display: flex;
            flex-direction: row;
            container-type: inline-size;
        }

        .melo-testimonial {
            box-sizing: border-box;
            padding: 1.5rem 4rem;
            position: relative;
            font-style: italic;
            font-size: 1.1rem;
            font-family: Verdana, sans-serif;
            line-height: 1.7rem;
            display: flex;
            align-items: center;
            column-gap: 2rem;
            row-gap: 1rem;
            min-width: 100%;
            justify-content: center;
            container-type: inline-size;
        }

        .melo-testimonial-picture {
            height: 6rem;
            min-width: 6rem;
            border-radius: 50%;
            box-shadow: 0 2px 4px #00000033;
            aspect-ratio: 1/1;
            object-fit: cover;
        }

        .melo-testimonial-name {
            text-align: right;
            font-weight: bold;
            margin: 0.6rem 0 0.8rem;
        }

        @media (max-width: 761px) {
            .melo-testimonial-picture {
                height: 7.2rem;
                min-width: 7.2rem;
            }
        }

        @media (max-width: 500px) {
            .melo-testimonial {
                flex-direction: column;
                padding: 2.5rem;
            }
        }

        /* melo-image */
        .melo-image-wrapper {
            width: 100%;
        }

        .melo-image-wrapper figure {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
        }

        .melo-image-wrapper figure figcaption {
            font-family: var(--fontMontserrat);
            font-style: italic;
            font-size: 0.8rem;
            line-height: 2rem;
            width: 100%;
        }

        .melo-image {
            vertical-align: middle;
            height: auto;
            max-width: 100%;
        }

        /* ---------------------- MELO BLOCK STYLES SPECIFIC ---------------------- */

        /* melo-image-1234 */
        #melo-image-wrapper-1234 {
            margin: 40px auto 40px auto;
        }

        #melo-image-wrapper-1234 figure figcaption {
            text-align: left;
            width: 400px;
            /* 100% for full width */
        }

        #melo-image-1234 {
            box-shadow: var(--meloShadow);
            border-radius: 12px;
            width: 400px;
            /* 100% for full width */
        }

        @media (max-width:700px) {
            #melo-image-wrapper-1234 {
                max-width: 100vw;
                border-radius: 0;

            }

            #melo-image-1234 {
                width: 100%;
                border-radius: 0;
            }

            #melo-image-wrapper-1234 figure figcaption {
                text-align: center;
                width: 100%;
            }
        }

        /* melo-testimonial-1234 */
        .melo-testimonial-slider-1234 {
            margin: 2rem auto;
            box-shadow: 0px 0px 8px 1px #d3d5d775;
            border-radius: 16px;
        }

        @keyframes melo-testimonial-slide-1234 {
            0% {
                transform: translateX(0);
            }

            16.67% {
                transform: translateX(0%);
            }

            33.33% {
                transform: translateX(-100%);
            }

            50% {
                transform: translateX(-100%);
            }

            66.67% {
                transform: translateX(-200%);
            }

            83.33% {
                transform: translateX(-200%);
            }

            100% {
                transform: translateX(-300%);
            }
        }

        .melo-testimonial-slider-1234:hover .melo-testimonial-1234 {
            animation-play-state: paused;
        }

        .melo-testimonial-1234 {
            animation: 21s melo-testimonial-slide-1234 infinite;
        }

        .melo-testimonial-1234:nth-child(odd) {
            background-color: #ffffff;
            color: #333333
        }

        .melo-testimonial-1234:nth-child(even) {
            background-color: #ededed;
            color: #333333;
        }

        /* melo-yt-video-123 */
        #melo-yt-video-123 {
            margin: 20px auto 40px auto;
            box-shadow: var(--meloShadow);
            border-radius: 12px;
            overflow: hidden;
            width: 900px;
            max-width: 90%;
        }

        /* melo-breadcrumb-1234 */
        #melo-breadcrumb-1234 {
            margin: 0 auto;
            background-color: var(--softBackground);
            border-top: 1px solid var(--c050);
            border-bottom: 1px solid var(--c050);
        }

        #melo-breadcrumb-1234 .melo-breadcrumb {
            padding: 8px 0;
        }

        /* hero-header-12345 */
        #hero-header-12345 {
            position: relative;
            container-type: inline-size;
            text-align: center;
            margin-top: 0px;
            margin-bottom: 0px;
        }

        #hero-header-12345 amp-img {
            z-index: 0;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        #hero-header-12345 amp-img img {
            object-fit: cover;
        }

        #hero-header-12345 .melo-hero-header-overlay {
            position: relative;
            z-index: 1;
            background-color: #565459b6;
            padding: 11vw 0 9.5vw 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        #hero-header-12345 .melo-hero-header-banner {
            color: #ffffff;
            background-color: #f9a82f;
            border-radius: 5px;
            display: inline-block;
        }

        #hero-header-12345 .melo-hero-header-main-text {
            font-weight: 100;
            color: #ffffff;
            display: block;
        }

        #hero-header-12345 .melo-hero-header-secondary-text {
            font-weight: 500;
            color: #f9a82f;
            display: block;
        }

        #hero-header-12345 a {
            border-radius: 8px;
            border: 1px solid #ff0000;
            color: #ffffff;
            background-color: #fbaeae7a;
            display: inline-block;
        }

        /* melo-dates-12345 */
        #melo-dates-12345 {
            margin: 32px auto 16px auto;
        }

        #melo-dates-12345 .melo-dates-inner {
            border-left-color: #f9a82f;
        }

        #melo-dates-12345 .melo-dates-inner time,
        #melo-dates-12345 .melo-dates-inner .author {
            color: #f9a82f;
        }

        /* melo-toc-1234 */
        #melo-toc-1234 {
            margin: 16px auto 16px auto;
        }

        #melo-toc-1234 .melo-toc {
            border-left-color: #f9a82f;
        }

        /* melo-hr-1234 */
        #melo-hr-1234 hr {
            border-color: #e74343;
            width: 480px;
            margin: 48px auto 48px auto;
            box-shadow: var(--meloShadow);
        }

        /* melo-text-12345 */
        #melo-text-12345 {
            margin: 0px auto 32px auto;
        }

        /* melo-columns-1234 */
        #melo-columns-1234 {
            margin: 8px auto 32px auto;
            column-gap: 8px;
            row-gap: 8px;
        }

        #melo-column-1234-1 {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            max-width: 25%;
            background-color: var(--c050);
            padding: 16px;
            border: 1px solid #f9a82f;
            border-radius: 8px;
        }

        #melo-column-1234-2 {
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: center;
            max-width: 25%;
            background-color: var(--c050);
            padding: 16px;
            border: none;
            border-radius: 0px;
        }

        #melo-column-1234-3 {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            max-width: 50%;
            background-color: none;
            padding: 16px;
            border: none;
            border-radius: 0px;
        }

        /* melo-button-12345 */
        #melo-button-12345 {
            text-align: center;
        }

        #melo-button-12345 a,
        button {
            padding: 1rem 2rem;
            font-size: 1.2rem;
            border-radius: 8px;
            border: 1px solid red;
            background-color: #fbaeae;
            box-shadow: var(--meloShadow);
            margin: 20px auto 20px auto;
            color: #ffffff;
            max-width: 50%;
            font-weight: bold;
        }
    </style>

</head>

<body [class]="darkMode.enabled ? 'dark-mode' : 'light-mode'" class="dark-mode">

    <!-- Mobile menu -->
    <amp-sidebar id="melo-mobile-menu" class="melo" layout="nodisplay" side="left"
        on="sidebarClose:AMP.setState({sidebarState: {isOpen: false}});">
        <nav class="melo melo-scroll-container" aria-label="Main Menu">
            <ul class="melo">
                <li>
                    <a href="https://justplay.systems/test-page" class="melo-two-line-ellipsis">String
                        Fragment
                        System String Fragment System blah blah isgdiushiushiudshi</a>
                </li>
                <li>
                    <a href="https://string.systems/" class="melo-two-line-ellipsis">String Systems
                        Website</a>
                </li>
                <li>
                    <a href="https://string.systems/about" class="melo-two-line-ellipsis">About/Contact</a>
                </li>
                <li id="dark-mode-li">
                    <div class="melo-dark-mode-div-mobile">
                        <label class="dark-mode-toggle-switch">
                            <input type="checkbox" on="change:AMP.setState({darkMode: {enabled: event.checked}})"
                                [checked]="darkMode.enabled" checked aria-labelledby="dark-mode-label-mob">
                            <span class="-dark-mode-toggle-slider"></span>
                        </label>
                        <p>DARK MODE</p>
                    </div>
                </li>
            </ul>
        </nav>
    </amp-sidebar>

    <!-- Initialize dark mode state -->
    <amp-state id="darkMode">
        <script type="application/json">
                {
                    "enabled": true
                }
            </script>
    </amp-state>

    <!-- Initialize sidebar state -->
    <amp-state id="sidebarState">
        <script type="application/json">
                {
                    "isOpen": false
                }
            </script>
    </amp-state>

    <!-- Wrap the AMP state components in a container -->
    <div class="amp-state-container">
        <!-- Add AMP bindings to body class -->
        <amp-bind-macro id="updateDarkMode" arguments="enabled"
            expression="enabled ? 'dark-mode' : 'light-mode'"></amp-bind-macro>
    </div>

    <!-- <Header> Start melo-block -->
    <header class="melo" itemscope itemtype="https://schema.org/Organization">

        <!-- header-nav -->

        <div class="melo melo-container-wrapper melo-header-nav-wrapper">
            <nav id="melo-header-nav-1234" class="melo melo-header-nav" aria-label="Main Menu">
                <div class="melo-hamburger-menu-wrapper">
                    <button class="melo-hamburger-menu"
                        [class]="'melo-hamburger-menu' + (sidebarState.isOpen ? ' open' : '')"
                        on="tap:melo-mobile-menu.toggle,AMP.setState({sidebarState: {isOpen: !sidebarState.isOpen}})"
                        [aria-expanded]="sidebarState.isOpen" aria-expanded="false" aria-controls="melo-mobile-menu"
                        aria-label="Toggle navigation menu">
                        <span class="hamburger-bar"></span>
                        <span class="hamburger-bar"></span>
                        <span class="hamburger-bar"></span>
                    </button>
                </div>
                <a id="logoAnchor" href="/" aria-label="Home">
                    <amp-img id="logo" src="https://effectivemusicpractice.b-cdn.net/assets/logo/emp-logo-984px-dm.webp"
                        [src]="darkMode.enabled ?
                            'https://effectivemusicpractice.b-cdn.net/assets/logo/emp-logo-984px-dm.webp' :
                            'https://effectivemusicpractice.b-cdn.net/assets/logo/emp-logo-984px.webp'" srcset="https://effectivemusicpractice.b-cdn.net/assets/logo/emp-logo-984px-dm.webp 1x,
                            https://effectivemusicpractice.b-cdn.net/assets/logo/emp-logo-984px-dm-2x.webp 2x"
                        [srcset]="darkMode.enabled ?
                            'https://effectivemusicpractice.b-cdn.net/assets/logo/emp-logo-984px-dm.webp 1x, https://effectivemusicpractice.b-cdn.net/assets/logo/emp-logo-984px-dm-2x.webp 2x' :
                            'https://effectivemusicpractice.b-cdn.net/assets/logo/emp-logo-984px.webp 1x, https://effectivemusicpractice.b-cdn.net/assets/logo/emp-logo-984px-2x.webp 2x'"
                        alt="Project Name Logo" width="216" height="48" layout="responsive">
                    </amp-img>
                </a>
                <!-- use backend logic to decide if a link is same as current url and add class for current page
                    accordingly -->
                <ul>
                    <li class="melo-header-nav-highlighted-link">
                        <a href="/amp/test-page1">45 String Fragment System</a>
                    </li>
                    <li>
                        <a href="https://string.systems">String Systems Website</a>
                    </li>
                    <li>
                        <a href="https://string.systems/about">About/Contact</a>
                    </li>
                </ul>


                <amp-social-share type="system" width="48" height="48" class="melo-share-icon"
                    data-param-text="TITLE - https://codetest.b-cdn.net/test-page/index.html" style="background: none;">
                    <div role="button" tabindex="0">
                        <svg id="melo-share-icon-ios" width="48px" height="48px" viewBox="0 0 24 24"
                            style="padding: 8px;">
                            <g stroke="none" stroke-width="1" fill-rule="evenodd">
                                <g fill="var(--c700)" fill-rule="nonzero">
                                    <path
                                        d="M16 5l-1.42 1.42-1.59-1.59V16h-1.98V4.83L9.42 6.42 8 5l4-4 4 4zm4 5v11c0 1.1-.9 2-2 2H6c-1.11 0-2-.9-2-2V10c0-1.11.89-2 2-2h3v2H6v11h12V10h-3V8h3c1.1 0 2 .89 2 2z">
                                    </path>
                                </g>
                            </g>
                        </svg>
                    </div>
                </amp-social-share>
                <div class="melo-dark-mode-div-wrapper">
                    <div class="melo-dark-mode-div">
                        <label class="dark-mode-toggle-switch">
                            <input type="checkbox" role="switch"
                                on="change:AMP.setState({darkMode: {enabled: event.checked}})"
                                [checked]="darkMode.enabled" checked aria-labelledby="dark-mode-label">
                            <span class="-dark-mode-toggle-slider"></span>
                        </label>
                        <p id="dark-mode-label">DARK MODE</p>
                    </div>
                </div>
            </nav>
        </div>

        <!-- <Header> CLOSE melo-block -->

    </header>

    <main itemscope itemtype="https://schema.org/HowTo">

        <!-- melo-utility-section-opening-tag START -->
        <section id="melo-utility-section-opening-tag-1234">
            <!-- melo-utility-section-opening-tag END -->

            <!-- melo-hero-header -->
            <div id="hero-header-12345" class="melo melo-hero-header">
                <amp-img layout="fill"
                    src="https://effectivemusicpractice-1701768237300-fast.b-cdn.net/assets/melo-block/hero-header/guitar-1209318_1920-1702931812653-1024px.webp"
                    srcset="https://effectivemusicpractice-1701768237300-fast.b-cdn.net/assets/melo-block/hero-header/guitar-1209318_1920-1702931812653-1024px.webp 1024w,
                            https://effectivemusicpractice-1701768237300-fast.b-cdn.net/assets/melo-block/hero-header/guitar-1209318_1920-1702931812653-2048px.webp 2048w"
                    alt="Guitar background">
                </amp-img>
                <div class="melo-hero-header-overlay">
                    <p class="melo-hero-header-banner melo-width-boxed">UPGRADE YOUR CHORD SKILLS</p>
                    <h1 itemprop="name" class="melo-hero-header-main-text melo-width-boxed">Barre Ελληνικά Workshop</h1>
                    <p itemprop="description" class="melo-hero-header-secondary-text melo-width-boxed">UNDERSTANDING
                        MOVEABLE CHORDS</p>
                    <a href="https://w3docs.com" target="_top">Call To Action</a>
                </div>
            </div>

            <!-- melo-utility-section-closing-tag START -->
        </section>
        <!-- melo-utility-section-closing-tag END -->

        <!-- melo-breadcrumb START -->
        <div class="melo melo-container-wrapper" id="melo-breadcrumb-1234">
            <div class="melo-breadcrumb melo-width-boxed">
                <ul>
                    <li><a href="/">justplay.systems</a></li>
                    <li><a href="/guitar-chords">Chords & Rhythm Guitar</a></li>
                    <li><span>Barre Chord Lesson</span></li>
                </ul>
            </div>
        </div>
        <!-- melo-breadcrumb END -->


        <!-- melo-dates  START -->
        <div id="melo-dates-12345" class="melo melo-container-wrapper melo-width-boxed">
            <div class="melo-dates-inner">
                <p>Published on: <time datetime="2023-11-16">November 16, 2023</time></p>
                <p>Last updated on: <time datetime="2025-03-07">March 7, 2025</time></p>
                <p>Author: <span class="author" itemscope itemtype="https://schema.org/Person"><a
                            href="https://string.systems/about" itemprop="url"><span itemprop="name">Prokopis
                                Skordis</span></a></span></p>
            </div>
        </div>
        <!-- melo-dates  END -->

        <!-- melo-toc START -->
        <div id="melo-toc-1234" class="melo melo-container-wrapper">
            <nav class="melo-toc melo-width-boxed">
                <ul>
                    <li>
                        <p class="section-header">Section 1</p>
                        <ul>
                            <li>
                                <a href="#link1">Link 1</a>
                            </li>
                            <li>
                                <a href="#link2">Link 2</a>
                            </li>
                            <li>
                                <a href="#link3">Link 3</a>
                            </li>
                            <li>
                                <a href="#link4">Link 4</a>
                            </li>
                        </ul>
                    </li>
                    <li><a class="section-header" href="#section2">Section 2</a>
                        <ul>
                            <li>
                                <a href="#link5">Link 5</a>
                            </li>
                            <li>
                                <a href="#link6">Link 6</a>
                            </li>
                            <li>
                                <a href="#link7">Link 7</a>
                            </li>
                            <li>
                                <a href="#link8">Link 8</a>
                            </li>
                        </ul>
                    </li>
                    <li><a class="section-header" href="#lonelink1">Lonely Link 1</a></li>
                    <li><a class="section-header" href="#lonelink2">Lonely Link 2</a></li>
                </ul>
            </nav>
        </div>
        <!-- melo-toc END -->

        <!-- melo-hr START-->
        <div class="melo melo-container-wrapper" id="melo-hr-1234">
            <hr class="melo melo-width-boxed">
        </div>
        <!-- melo-hr END-->

        <!-- melo-utility-section-opening-tag START -->
        <section id="melo-utility-section-opening-tag-1235" itemprop="step" itemscope
            itemtype="http://schema.org/HowToStep">
            <!-- melo-utility-section-opening-tag END -->

            <!-- melo-text START -->
            <div class="melo-container-wrapper" id="melo-text-12345">
                <div class="melo melo-text melo-width-boxed">
                    <h1>This is an H1 title</h1>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut
                        labore
                        et
                        dolore magna
                        aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
                        commodo
                        consequat.
                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
                        pariatur.
                        Excepteur
                        sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est
                        laborum.
                    </p>
                    <h2>This is an H2 title</h2>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut
                        labore
                        et
                        dolore magna
                        aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
                        commodo
                        consequat.
                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
                        pariatur.
                        Excepteur
                        sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est
                        laborum.
                    </p>
                    <h3>This is an H3 Title</h3>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut
                        labore
                        et
                        dolore magna
                        aliqua. <a target="_blank" rel="noopener noreferrer" href="https://string.systems">Ut enim ad
                            minim
                            veniam</a>, quis
                        nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure
                        dolor
                        in
                        reprehenderit
                        in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat
                        cupidatat
                        non
                        proident, sunt
                        in culpa qui officia deserunt mollit anim id est laborum.</p>
                    <ul>
                        <li>
                            <p>This is a bullet</p>
                        </li>
                        <li>
                            <p>And another</p>
                        </li>
                    </ul>
                    <ol>
                        <li>
                            <p>Item <em>number</em> one</p>
                        </li>
                        <li>
                            <p>Item number <strong>two</strong></p>
                        </li>
                    </ol>
                    <p></p>
                    <p class="melo-text-align-center">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
                        eiusmod
                        tempor
                        incididunt
                        ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco
                        laboris
                        nisi ut aliquip
                        ex ea commodo consequat. Duis aute irure dolor in reprehenderit in velit esse cillum dolore eu
                        fugiat
                        nulla
                        pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt
                        <s>mollit</s> anim id
                        est <u>laborum</u>.
                    </p>
                    <p></p>
                    <p class="melo-text-align-right">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
                        eiusmod
                        tempor
                        incididunt
                        ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco
                        laboris
                        nisi ut aliquip
                        ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum
                        dolore eu
                        fugiat
                        nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia
                        deserunt
                        mollit anim id
                        est laborum.</p>
                    <p class="melo-text-align-justify"></p>
                    <p class="melo-text-align-justify">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
                        eiusmod
                        tempor incididunt
                        ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco
                        laboris
                        nisi ut aliquip
                        ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum
                        dolore eu
                        fugiat
                        nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia
                        deserunt
                        mollit anim id
                        est laborum.</p>
                    <p></p>
                    <pre><code>This is some code</code></pre>
                    <blockquote>
                        <p><em>This is a quote nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in
                                culpa nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa</em>
                        </p>
                    </blockquote>
                    <p></p>
                    <h3>This is an H3 Title</h3>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut
                        labore
                        et
                        dolore magna
                        aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
                        commodo
                        consequat.
                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
                        pariatur.
                        Excepteur
                        sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est
                        laborum.
                    </p>
                </div>
            </div>
            <!-- melo-text END -->

            <!-- melo-utility-section-closing-tag START -->
        </section>
        <!-- melo-utility-section-closing-tag END -->

        <!-- melo-utility-article-opening-tag START -->
        <article id="melo-utility-article-opening-tag-1234" itemprop="step" itemscope
            itemtype="http://schema.org/HowToStep">
            <!-- melo-utility-article-opening-tag END -->

            <!-- Columns -->
            <div class="melo melo-container-wrapper">
                <div id="melo-columns-1234" class="melo melo-columns melo-width-boxed">
                    <div id="melo-column-1234-1" class="melo-column">
                        <div class="melo-text melo-width-boxed">
                            <h2>This is an H2 title</h2>
                            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                                incididunt
                                ut
                                labore
                                et
                                dolore magna
                                aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut
                                aliquip ex ea
                                commodo
                                consequat.
                                Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu
                                fugiat
                                nulla
                                pariatur.
                                Excepteur
                                sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim
                                id
                                est
                                laborum.
                            </p>
                        </div>
                    </div>
                    <div id="melo-column-1234-2" class="melo-column">
                        <div class="melo-text">
                            <p>second Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                                incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
                                exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute
                                irure
                                dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
                                pariatur.
                                Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt
                                mollit anim id est laborum.</p>
                        </div>
                    </div>
                    <div id="melo-column-1234-3" class="melo-column">
                        <div class="melo-text">
                            <p>third Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                                incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
                                exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute
                                irure
                                dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
                                pariatur.
                                Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt
                                mollit anim id est laborum.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- melo-utility-section-opening-tag START -->
            <section id="melo-utility-section-opening-tag-1236" itemprop="step" itemscope
                itemtype="http://schema.org/HowToStep">
                <!-- melo-utility-section-opening-tag END -->

                <!-- melo-yt-video START -->
                <div class="melo melo-container-wrapper">
                    <div id="melo-yt-video-123" class="melo-yt-video">
                        <amp-youtube id="youtube-video-123" data-videoid="2RpKmeedA4s" layout="responsive" width="480"
                            height="270" credentials="omit" data-param-rel="0" data-param-modestbranding="1">
                        </amp-youtube>
                    </div>
                </div>

                <!-- Schema.org structured data -->
                <script type="application/ld+json">
                    {
            "@context": "https://schema.org",
            "@type": "VideoObject",
            "name": "Learn The Fretboard In 7 Steps - Fun and Effective System",
            "description": "Learn how to solo over 12 bar blues, and how to switch to the right scales, at the right place on the guitar fretboard.",
            "thumbnailUrl": "https://i.ytimg.com/vi/2RpKmeedA4s/maxresdefault.jpg",
            "uploadDate": "2016-09-13T07:10:34Z",
            "duration": "PT13M45S",
            "embedUrl": "https://www.youtube.com/embed/2RpKmeedA4s",
            "interactionCount": "196675"
        }
        </script>
                <!-- melo-yt-video END -->

                <!-- melo-utility-section-closing-tag START -->
            </section>
            <!-- melo-utility-section-closing-tag END -->

            <!-- melo-testimonial-slider START -->
            <div class="melo melo-container-wrapper" id="melo-testimonial-slider-1234">
                <div class="melo-testimonial-slider melo-testimonial-slider-1234 melo-width-boxed">
                    <div class="melo-testimonial-row">
                        <div class="melo-testimonial melo-testimonial-1234"><img class="melo-testimonial-picture"
                                src="https://string-systems.b-cdn.net/assets/melo-block-testimonial-pics/1700065511270-henderson-suttle.jpg"
                                alt="Reviewer Picture">
                            <div>
                                <p><br>"Prokopis genuinely cares about his students. His teaching approach is
                                    one-of-a-kind,
                                    and his encouragement and clarity have greatly contributed to my improvement. I am
                                    grateful for
                                    his guidance and support."<br></p>
                                <p class="melo-testimonial-name">Henderson Suttle</p>
                            </div>
                        </div>
                        <div class="melo-testimonial melo-testimonial-1234"><img class="melo-testimonial-picture"
                                src="https://string-systems.b-cdn.net/assets/melo-block-testimonial-pics/1700067511017-daniel-cooper.jpg"
                                alt="Reviewer Picture">
                            <div>
                                <p><br>"Prokopis’ skillful teaching has helped me make tremendous progress in a very
                                    short
                                    time.
                                    Much appreciated!"<br></p>
                                <p class="melo-testimonial-name">Daniel Cooper</p>
                            </div>
                        </div>
                        <div class="melo-testimonial melo-testimonial-1234"><img class="melo-testimonial-picture"
                                src="https://string-systems.b-cdn.net/assets/melo-block-testimonial-pics/1700068693228-George-Vetsos.jpg"
                                alt="Reviewer Picture">
                            <div>
                                <p><br>"As a guitar teacher myself, Prokopis' unique methods have not only helped me
                                    improve
                                    my own
                                    playing but have also influenced and shaped my own teaching style. I am truly
                                    grateful
                                    for his
                                    guidance and the impact he has had on my musical journey."<br></p>
                                <p class="melo-testimonial-name">George Vetsos</p>
                            </div>
                        </div>
                        <div class="melo-testimonial melo-testimonial-1234"><img class="melo-testimonial-picture"
                                src="https://string-systems.b-cdn.net/assets/melo-block-testimonial-pics/1700065511270-henderson-suttle.jpg"
                                alt="Reviewer Picture">
                            <div>
                                <p><br>"Prokopis genuinely cares about his students. His teaching approach is
                                    one-of-a-kind,
                                    and his encouragement and clarity have greatly contributed to my improvement. I am
                                    grateful for
                                    his guidance and support."<br></p>
                                <p class="melo-testimonial-name">Henderson Suttle</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- melo-testimonial-slider END -->

            <!-- melo-image START -->
            <div class="melo melo-container-wrapper">
                <div id="melo-image-wrapper-1234" class="melo-image-wrapper melo-width-boxed">
                    <figure>
                        <amp-img id="melo-image-1234" class="melo-image"
                            src="https://melo-assets.b-cdn.net/955869c4-d131-4a02-b5dd-f356c5d57af4.jpeg"
                            srcset="https://melo-assets.b-cdn.net/955869c4-d131-4a02-b5dd-f356c5d57af4.jpeg 1x, https://melo-assets.b-cdn.net/955869c4-d131-4a02-b5dd-f356c5d57af4.jpeg 2x"
                            alt="This is an image of a guitar" width="1920" height="1080" layout="responsive">
                        </amp-img>
                        <figcaption>Caption goes here</figcaption>
                    </figure>
                </div>
            </div>
            <!-- melo-image END -->

            <!-- melo-button START -->
            <div class="melo melo-container-wrapper" id="melo-button-12345">
                <a class="melo-button melo-width-boxed" href="https://string.systems" target="_blank">press
                    button Lorem ipsum dolor sit amet consectetur !</a>
            </div>
            <!-- melo-button END -->
    </main>

    <footer class="melo melo-container-wrapper" id="melo-footer">
        <div class="melo-footer-wrapper melo-width-boxed">
            <p><a href="https://courses.string.systems/p/free-courses">Free Mini Courses</a></p>
            <p><a href="https://courses.string.systems/p/premium-courses">Premium Courses</a></p>
            <p><a href="https://courses.string.systems/p/pps">Access All Courses</a></p>
            <p><a href="https://string.systems/blog">String Systems Blog</a></p>
            <p><a href="https://string.systems/private-lessons">Online Private Lessons</a></p>
            <p><a href="https://urlgeni.us/youtube/channel/string">YouTube Channel</a></p>
            <p><a href="https://string.systems/app">String Courses App</a></p>
            <p><a href="https://help.string.systems/">Help Center</a></p>
            <p><a href="https://courses.string.systems/sign_in">Log in to my courses</a></p>
        </div>
        <div class="melo-footer-bottom-centered melo-width-boxed">
            <hr>
            <br>
            <p>Copyright &copy; 2017-2025 Prokopis Skordis</p>
            <p><a href="https://string.systems/about">About</a> / <a
                    href="https://string.systems/privacy-policy">Privacy Policy</a> / <a
                    href="https://string.systems/terms">Terms Of Use</a></p>
            <p>String Systems aka EffectiveMusicPractice LTD</p>
            <p>Nikolaou Skoufa 29, Limassol 3016, Cyprus.</p>
            <p>email: <EMAIL></p>
        </div>
    </footer>


    </div>

</body>

</html>