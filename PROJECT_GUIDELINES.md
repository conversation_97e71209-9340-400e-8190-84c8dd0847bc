# MeloBlock Prototypes Guidelines

This document outlines the organization, coding standards, and best practices for the MeloBlock Prototypes project. MeloBlock Prototypes serves two primary goals:

1. **Component Development**: Creating reusable, portable website components (MeloBlocks) that can be embedded in various websites while maintaining consistent appearance and functionality.

2. **Website Builder Templates**: Providing the foundational templates for a website builder that can both publish complete pages and export independent blocks. These templates define the structure for both regular HTML pages and their AMP counterparts.

## Project Overview

This project serves as the template foundation for a website builder software. When completed, the project pages will include at least one instance of every MeloBlock type developed, serving as both a showcase and a testing environment for all components.

The key components of the project are:

- **test-page**: The standard HTML template that serves as the base for the website builder
- **amp/test-page**: The AMP-compatible version of the standard template
- **test-page-melo**: An example page generated by the website builder (for testing purposes)
- **amp/test-page-melo**: The AMP version of the example page

Development primarily focuses on optimizing the base templates (`test-page` and `amp/test-page`). The `-melo` versions are used to test the output of the website builder by copying the built code into these files.

### Key Principles:

1. **Parallel Structure**: Maintain parallel directory structures between standard and AMP versions
2. **Asset Centralization**: Keep all assets in the assets directory, properly categorized
3. **Flat Hierarchy**: Keep directory nesting minimal for better maintainability

## Coding Conventions

### HTML

- Use HTML5 doctype: `<!doctype html>`
- Include appropriate language attribute: `<html lang="en">`
- Organize head elements in a consistent order:
  1. Essential meta tags (charset, viewport, robots)
  2. Page title
  3. Primary meta tags (description)
  4. Open Graph meta tags
  5. Twitter meta tags
  6. Preloaded resources
  7. Canonical and AMP links
  8. Icons
  9. Manifest
  10. Styles
- Use semantic HTML elements (`header`, `main`, `section`, `footer`, etc.)
- Include appropriate Schema.org markup for SEO

### CSS

- Use CSS variables for theming and consistent styling
- Organize CSS in logical sections with clear comments
- Use container queries where possible for responsive design in standard pages
- For AMP pages, use media queries instead of container queries (AMP doesn't support container queries)
- Follow mobile-first approach for media queries
- Keep selectors as simple as possible
- Use consistent naming conventions for classes (see [CSS Organization](#css-organization))
- Use multiples of 4 for sizing values (padding, margin, width, height, etc.)
- Use rem units for text sizes for better accessibility and scaling
- Use container-relative units (cqw) or viewport-relative units (vw) for responsive text in specific components like hero sections

### JavaScript

- Minimize JavaScript usage for better performance and AMP compatibility
- On AMP pages, replace JavaScript with AMP compatible equivalents.
- When necessary, use vanilla JavaScript over frameworks
- Keep event listeners minimal and efficient
- Ensure all interactive elements are accessible via keyboard

## Component Organization

The project uses a component-based approach called "MeloBlocks":

### MeloBlock Structure

Each MeloBlock is a self-contained component with:

1. **HTML Structure**: The semantic markup
2. **CSS Styling**: Component-specific styles
3. **JavaScript Behavior**: Minimal and only when necessary

### MeloBlock Types

- **melo-header-nav**: Navigation component
- **melo-hero-header**: Hero banner component
- **melo-columns**: Multi-column layout component
- **melo-text-native**: Text content component
- **melo-hr**: Horizontal rule component
- more will be added to this list as development continues

### MeloBlock Implementation Guidelines

1. Wrap all MeloBlocks with `.melo` (scoped reset) and `.melo-container-wrapper` (prepare for container queries) classes
2. Use unique IDs for each MeloBlock instance (e.g., `melo-hero-header-1234`)
3. Include component-specific classes (e.g., `melo-hero-header`)
4. Use `.melo-width-boxed` for content width constraints
5. Keep component styles scoped to the component
6. Comment the beginning and end of each MeloBlock in the HTML

Example:
```html
<!-- melo-utility-section-opening-tag START -->
<section id="melo-utility-section-opening-tag-1234">
<!-- melo-utility-section-opening-tag END -->
    <!-- melo-hero-header START -->
    <div id="hero-header-12345" class="melo melo-hero-header">
        <!-- Component content -->
    </div>
    <!-- melo-hero-header END -->
<!-- melo-utility-section-closing-tag START -->
</section>
<!-- melo-utility-section-closing-tag END -->
```

**Note on Structural Elements**: Some semantic HTML elements function as MeloBlocks that users can add through the page builder:

- **Template Structure Elements**: `<header>` (containing only the navigation), `<main>`, and `<footer>` tags are part of the standard template structure and are not MeloBlocks themselves. The header will always end after the navbar, and the main tag will start immediately after the header and end just before the footer.
- **Content Structure Elements**: `<section>`, `</section>`, `<article>`, and `</article>` are all MeloBlocks that users can add to organize content within the main section.

These structural elements work together with content-specific MeloBlocks (like hero-header) to build complete pages. Each MeloBlock has a unique ID that enables anchor links for in-page navigation and specific styling. The comments in the example above show how these structural elements are marked as MeloBlocks in the HTML.

## AMP Compatibility

The project maintains parallel AMP versions of pages for better performance and compatibility. Unlike standard MeloBlocks which may be embedded in other websites, AMP pages are always used as complete standalone pages.

### AMP Guidelines

1. Follow the [AMP HTML specification](https://amp.dev/documentation/guides-and-tutorials/learn/spec/amphtml/)
2. Replace standard HTML tags with AMP equivalents where required. For example:
   - `<img>` → `<amp-img>`
   - `<video>` → `<amp-video>`
   - Interactive elements → appropriate AMP components
3. Include required AMP boilerplate code
4. Use `<style amp-custom>` for custom styles
5. Avoid disallowed JavaScript and rely on AMP components instead
6. Keep CSS under the 75KB limit
7. Maintain canonical links between standard and AMP versions

### AMP Component Usage

- Use `amp-sidebar` for mobile navigation menus
- Use `amp-bind` for state management
- Use `amp-social-share` for social sharing functionality

### AMP Restrictions and Compliance

AMP enforces strict rules to ensure fast-loading, predictable pages. Always verify your code against these restrictions:

1. **CSS Restrictions**:
   - **NO `!important` declarations** - AMP prohibits the use of `!important` in CSS
   - No inline styles (use `<style amp-custom>` in the head instead)
   - No external stylesheets (except for allowlisted font providers)
   - Total CSS must be under 75KB
   - No `@import` in CSS

2. **JavaScript Restrictions**:
   - No custom JavaScript allowed (except within AMP components)
   - No event listeners in HTML (use AMP components and actions instead)
   - No `onclick`, `onmouseover`, or other event handler attributes
   - Use AMP's action system (`on="tap:..."`) for interactions

3. **HTML Restrictions**:
   - Certain HTML tags are prohibited or must be replaced with AMP versions
   - Custom elements must be prefixed with `amp-`
   - Some attributes are disallowed or have restrictions

4. **Layout and Rendering**:
   - No container queries (use media queries instead)
   - Layout system uses specific AMP attributes (`layout`, `width`, `height`)
   - Explicit dimensions required for many elements

5. **Testing Requirements**:
   - **Always test AMP changes in the AMP validator**
   - Use browser developer tools with AMP validation
   - Test on multiple devices and screen sizes

When implementing features that work in standard HTML, always check if they're compatible with AMP before adding them to AMP pages. When in doubt, consult the [AMP documentation](https://amp.dev/documentation/) or use the [AMP Validator](https://validator.ampproject.org/).

## CSS Organization

### Inline CSS Approach

All CSS should be included directly in the HTML file using `<style>` tags (or `<style amp-custom>` for AMP pages). No external CSS files should be used. This approach ensures that all styles are contained within the page, making the templates more portable, faster, and easier to manage within the website builder environment.

**Note on AMP vs Standard Versions**: While portability is a key concern for standard MeloBlocks (which may be embedded in other websites), AMP versions will only be used as complete pages. However, for consistency and maintainability, both versions should follow the same CSS organization pattern whenever possible.

### CSS Structure in the `<head>`

CSS should be organized in the `<head>` section in this specific order:

1. **General CSS**: This section contains global styles, color variables, and page-level styles specific to the current website. It is present only in pages that are produced by the website builder. MeloBlocks should not depend on these styles. It includes the `:root` CSS variables, basic element styling (body, html), any reset or normalize styles, and styles which are specific to the website builder and won't be embedded in other websites. These styles apply to the entire page but should NOT be relied upon by MeloBlocks, as they won't be available when MeloBlocks are embedded in other websites.

2. **Melo CSS**: This section contains self-contained styles that are applicable to all types of MeloBlocks. These are the foundational styles that ensure consistent behavior across all MeloBlock components regardless of the hosting website. This includes basic margin/padding resets, container behavior, and width constraints. These styles should be designed to override or be independent of the hosting website's styles to ensure portability.

3. **Nav Bar & Footer Styles**: This section contains styles for the navigation menu and footer components. Although these components are only used in Melo Static Pages produced by the website builder (and not embedded in other websites), they are placed in their own dedicated section due to style cascading considerations and because they will be customizable in the builder. Unlike Melo Block Styles which should be copy-paste styles with no changes or variability, the header and footer are unique for every site and require their own section.

4. **Melo Block Styles**: This section contains component-type styles for the specific MeloBlocks used on the page. For example, all hero headers share common styling defined here, as do all column layouts, etc. These styles define the general appearance and behavior of each MeloBlock type and should be completely self-contained to ensure they work consistently when embedded in any website.

5. **Melo Block Styles Specific**: This section contains instance-specific styles for each MeloBlock on the page, referenced by their unique IDs. While the general component styles are defined in the previous section, any customizations for specific instances (such as different background colors, spacing, or text alignment for a particular hero header) are defined here. Like the other MeloBlock styles, these should be self-contained and not depend on the General CSS section.

### CSS Variables

CSS variables should be defined in the `:root` selector within the General CSS section. These variables include color schemes, spacing values, shadow definitions, and font stacks that are used throughout the page. Using CSS variables ensures consistency and makes it easier to implement features like dark mode.

### Separation of Concerns

This structured approach to CSS organization creates a clear separation of concerns:

- General styles apply to the entire page
- Melo CSS applies to all MeloBlock components
- Nav Bar & Footer styles handle site-specific navigation and footer components
- Component-type styles define the look and behavior of each type of MeloBlock
- Instance-specific styles customize individual MeloBlocks

This hierarchy makes the CSS more maintainable and allows for easier customization within the website builder environment.

### CSS Units and Sizing

For consistency and better design harmony across the project, follow these guidelines:

- **Spacing Values**: Use multiples of 4 for all spacing values (margin, padding, gap, etc.)
  - Examples: 4px, 8px, 12px, 16px, 20px, 24px, 32px, 48px, 64px
  - This creates a consistent rhythm throughout the interface

- **Text Sizing**:
  - Use `rem` units for most text elements for better accessibility and scaling
  - Use `clamp()` with container-relative units (`cqw`) or viewport-relative units (`vw`) for responsive text in hero sections and other large display text
  - Example: `font-size: clamp(28px, 9cqw, 76px);` (for standard pages)
  - Example: `font-size: clamp(28px, 9vw, 76px);` (for AMP pages)

- **Avoid Inline Styles**:
  - Never use inline styles (`style` attribute) in HTML
  - Place all styles in the appropriate CSS section
  - This is especially important for AMP pages where inline styles are prohibited

- **Width and Height**:
  - Use percentage or `auto` for fluid layouts when possible
  - Use multiples of 4 for fixed dimensions
  - Use `max-width` instead of fixed `width` for better responsiveness

### Class Naming Conventions

- Use `melo-` prefix for all project-specific classes
- Follow a component-based naming pattern:
  - `melo-[component]` for the main component
  - `melo-[component]-[element]` for elements within components
  - `melo-[component]-[element]-[modifier]` for variations
- Use descriptive, functional names rather than presentational names

### Element ID Naming Conventions

- Follow the same logic as class naming conventions for classes but append an id number to the end of the name. This number should be unique for each instance of the MeloBlock. It is generated by the website builder based on the database ID of the MeloBlock.

## Asset Management

### Images

- Use WebP format with fallbacks when necessary
- Provide multiple resolutions using `srcset` for responsive images
- Optimize all images for web use
- Preload critical above-the-fold images
- Use appropriate `alt` text for accessibility

### Fonts

- Montserrat is the primary font for all MeloBlocks
- For standard pages:
  - Use locally hosted Montserrat variable font in WOFF2 format
  - Preload the font file for critical text elements
  - Define appropriate fallback fonts (Montserrat-fallback, Verdana, sans-serif)
- For AMP pages:
  - Use Google Fonts for Montserrat (AMP restriction)
  - Include the font using: `<link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">`
- Define appropriate `font-display` strategy (usually `swap`)
- Use consistent font variable: `--fontMontserrat: 'Montserrat', 'Montserrat-fallback', Verdana, sans-serif;`

### Icons

- Maintain icons in multiple sizes for different contexts
- Use appropriate formats (WebP for modern browsers, PNG for fallbacks and for Apple touch icons)
- Include favicon and Apple touch icons
- Ensure icons are accessible with proper aria attributes

## Accessibility Standards

### General Guidelines

- Ensure proper color contrast (WCAG AA minimum)
- Make all interactive elements keyboard accessible
- Use semantic HTML elements
- Include appropriate ARIA attributes when necessary
- Ensure all images have descriptive alt text
- Make hamburger menu icons transform into X icons when menus are open
- Ensure hamburger menu elements are accessible to screen readers

### Navigation Accessibility

- Ensure all navigation elements are keyboard accessible
- Use appropriate `aria-label` and `aria-expanded` attributes
- Make mobile navigation fully accessible

## Performance Optimization

### Loading Optimization

- Preload critical resources (not on AMP pages)
- Use appropriate image sizes and formats
- Minimize CSS and JavaScript
- Implement responsive images with `srcset`
- Use resource hints (`preconnect`, `dns-prefetch`) for external resources

### Rendering Optimization

- Minimize layout shifts
- Use hardware acceleration for animations
- Optimize CSS selectors
- Minimize DOM depth

## Dark Mode Implementation

The project supports both light and dark modes:

### Implementation Strategy

1. Define separate CSS variable sets for light and dark modes
2. Use class-based switching (`dark-mode` and `light-mode` classes)
3. Provide toggle UI in both desktop and mobile views
4. Persist user preference using local storage
5. Respect user's system preference as default

### Dark Mode Toggle

- Include toggle in header for desktop view
- Include toggle in mobile menu for mobile view
- Ensure toggle is keyboard accessible and screen reader friendly

## Responsive Design

### Approach

- For standard pages:
  - Use container queries for component-level responsiveness
  - Fall back to media queries for global layout changes
- For AMP pages:
  - Use media queries exclusively (container queries are not supported in AMP)
  - Ensure responsive behavior matches standard pages as closely as possible
- Follow a mobile-first approach for all media queries
- Use flexible units (%, rem, em) instead of fixed units (px) where appropriate
- Implement appropriate breakpoints for different device sizes

### Key Breakpoints

- Mobile: Up to 500px
- Tablet: 501px to 960px
- Desktop: 961px and above

### Container Queries

For standard pages, use container queries for component-level responsiveness:

```css
@container (max-width: 960px) {
    /* Component-specific responsive styles */
}
```

For AMP pages, convert container queries to equivalent media queries:

```css
@media (max-width: 960px) {
    /* Component-specific responsive styles */
}
```

When designing components, consider both approaches to ensure consistent behavior across standard and AMP versions.

---

This document is a living guide and will be updated as the project evolves.
