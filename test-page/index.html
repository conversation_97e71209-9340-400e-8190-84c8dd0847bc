<!doctype html>
<html lang="en">

<head>
    <!-- Essential Meta Tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1">
    <meta name="robots" content="index, follow, archive">

    <!-- Page Title -->
    <title>Solved: What Scales To Play Over 12 Bar Blues</title>

    <!-- Primary Meta Tags -->
    <meta name="description"
        content="Learn how to solo over 12 bar blues, and how to switch to the right scales, at the right place on the guitar fretboard.">

    <!-- Open Graph Meta Tags -->
    <meta property="og:url" content="https://justplay.systems/test-page">
    <meta property="og:site_name" content="Effective Music Practice">
    <meta property="og:title" content="Solved: What Scales To Play Over 12 Bar Blues">
    <meta property="og:description"
        content="Learn how to solo over 12 bar blues, and how to switch to the right scales, at the right place on the guitar fretboard.">
    <meta property="og:image"
        content="https://effectivemusicpractice.b-cdn.net/assets/images/share/what-scales-to-play-over-12-bar-blues-1200px-1701768237300.webp">
    <meta property="og:image:alt" content="Solved: What Scales To Play Over 12 Bar Blues">

    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Solved: What Scales To Play Over 12 Bar Blues">
    <meta name="twitter:description"
        content="Learn how to solo over 12 bar blues, and how to switch to the right scales, at the right place on the guitar fretboard.">
    <meta name="twitter:image"
        content="https://effectivemusicpractice.b-cdn.net/assets/images/share/what-scales-to-play-over-12-bar-blues-1200px-1701768237300.webp">

    <!-- Preload Montserrat for text melo-blocks -->
    <link rel="preload" href="/assets/fonts/Montserrat-VariableFont_wght.woff2" as="font" type="font/woff2"
        fetchpriority="high" crossorigin>

    <!-- Optionally preload the above the fold main image -->
    <link rel="preload" as="image"
        href="https://effectivemusicpractice-1701768237300-fast.b-cdn.net/assets/melo-block/hero-header/guitar-1209318_1920-1702931812653-1024px.webp"
        imagesrcset="https://effectivemusicpractice-1701768237300-fast.b-cdn.net/assets/melo-block/hero-header/guitar-1209318_1920-1702931812653-1024px.webp 1x, https://effectivemusicpractice-1701768237300-fast.b-cdn.net/assets/melo-block/hero-header/guitar-1209318_1920-1702931812653-2048px.webp 2x"
        fetchpriority="high">

    <!-- Canonical Tag -->
    <link rel="canonical" href="https://justplay.systems/test-page">
    <link rel="amphtml" href="https://justplay.systems/amp/test-page">

    <!-- Icons -->
    <link rel="icon" type="image/webp" sizes="16x16" href="/assets/icons/icon-16px.webp">
    <link rel="icon" type="image/webp" sizes="32x32" href="/assets/icons/icon-32px.webp">
    <link rel="icon" type="image/webp" sizes="192x192" href="/assets/icons/icon-192px.webp">
    <link rel="apple-touch-icon" sizes="87x87" href="/assets/icons/icon-87px.png" type="image/png">
    <link rel="apple-touch-icon" sizes="120x120" href="/assets/icons/icon-120px.png" type="image/png">
    <link rel="apple-touch-icon" sizes="152x152" href="/assets/icons/icon-152px.png" type="image/png">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/icons/icon-180px.png" type="image/png">

    <!-- JSON Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- STYLES -->
    <style>
        /* ---------------------- GENERAL CSS ---------------------- */
        /* Defaults for Melo Static Pages produced by the Melo Website Builder */

        * {
            margin: 0;
            padding: 0;
        }

        :root {
            --c000: #121212;
            --c050: #2D2D2D;
            --c100: #404040;
            --c200: #545454;
            --c300: #6C6C6C;
            --c400: #828282;
            --c500: #9C9C9C;
            --c600: #AAAAAA;
            --c700: #C8C8C8;
            --c800: #EBEBEB;
            --c900: #F7F7F7;
            --c950: #FFFFFF;
            --softBackground: #2d2d2d2c;
            --whiteBlack: #000;
            --projectAccent: #f9a82f;
            --projectSecondary: #ffffff;
            --transparentHeaderNav: #242424ed;
            --meloShadow: 0px 0px 10px #202020;
            /* Optional styles for non-transparent or sticker-like logos */
            /* user specifies logo background color */
            /* --logoBackgroundColor: #000000; */
        }

        body {
            background-color: var(--c000);
            color: var(--c900);
            line-height: 1.7rem;
        }

        html {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        html::-webkit-scrollbar {
            display: none;
        }

        body.menu-open {
            overflow: hidden;
            position: fixed;
            width: 100%;
        }

        header {
            position: sticky;
            top: 0;
            left: 0;
            right: 0;
            z-index: 12;
        }

        /* TESTING START */
        /*  These styles override defaults for simulating behavior of blocks in other sites. Experiment with fonts, font-sizes, heading sizes, list styles, link styles, form styles (esp. input), colors, paddings, margins, etc, and see how they affect my blocks. I need to make melo-blocks fully independent of the hosting document styles. */

        /*
        * {
            margin: 10px;
            padding: 10px;
        }

        body {
            background-color: red;
            color: purple;
            line-height: 5rem;
            font-family: monospace;
            font-size: 0.5rem;
        }

        input {
            background-color: blue;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            font-family: Impact, Haettenschweiler, 'Arial Narrow Bold', sans-serif;
            font-size: 4rem;
            color: burlywood
        }
        */

        /* TESTING END */

        /* ---------------------- MELO CSS ---------------------- */
        /* Needed for all/most melo-blocks. Add to the end of the <head> in other websites */

        @font-face {
            font-family: "Montserrat";
            src: url("/assets/fonts/Montserrat-VariableFont_wght.woff2") format("woff2 supports variations"),
                url("/assets/fonts/Montserrat-VariableFont_wght.woff2") format("woff2-variations");
            font-weight: 100 900;
            font-display: swap;
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        @font-face {
            font-family: "Montserrat";
            font-style: italic;
            src: url("/assets/fonts/Montserrat-Italic-VariableFont_wght.woff2") format("woff2 supports variations"),
                url("/assets/fonts/Montserrat-Italic-VariableFont_wght.woff2") format("woff2-variations");
            font-weight: 100 900;
            font-display: swap;
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        @font-face {
            font-family: "Montserrat-fallback";
            size-adjust: 102%;
            src: local("Verdana");
        }

        html {
            scroll-behavior: smooth;
            --meloShadow: 0px 0px 10px #d8d8d8;
            --fontMontserrat: 'Montserrat', 'Montserrat-fallback', Verdana, sans-serif;
        }

        .melo,
        .melo * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .melo-container-wrapper {
            container-type: inline-size;
        }

        .melo-width-boxed {
            max-width: 870px;
            margin: auto;
        }

        .melo-scroll-container::-webkit-scrollbar {
            display: none;
        }

        .melo-scroll-container {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .melo-two-line-ellipsis {
            display: -webkit-box;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            max-height: 2.5em;
            line-height: 1.25;
            width: 100%;
            word-break: break-word;
        }

        @container (max-width:953px) {
            .melo-width-boxed {
                max-width: 90cqw;
            }
        }

        /* ---------------------- NAV BAR & FOOTER STYLES ---------------------- */
        /* Only for Melo Static Pages produced by the Melo Website Builder. */

        /* Navigation Menu START */

        .melo-header-nav-wrapper {
            font-size: 14px;
            line-height: 0rem;
            top: 0;
            left: 0;
            right: 0;
            z-index: 12;
            background-color: var(--transparentHeaderNav);
            height: 64px;
            border-bottom: 1px solid var(--c200);
            container-type: inline-size;
            transform: translateZ(0);
            /* Force hardware acceleration */
            -webkit-transform: translateZ(0);
            will-change: transform;
            /* Optimize for animations */
        }

        .melo-header-nav {
            display: flex;
            height: 100%;
            max-width: 90%;
            margin: auto;
            padding: 8px 0;
            color: var(--c700);
            font-family: var(--fontMontserrat);
        }

        .melo-hamburger-menu-wrapper {
            width: auto;
            display: none;
            position: relative;
            z-index: 13;
            transition: ease 0.3s;
        }

        .melo-hamburger-menu-wrapper:hover {
            transform: scale(1.1);
        }

        .melo-hamburger-menu {
            margin: auto 8px auto 0;
            transition: transform 0.3s ease-in;
            background: none;
            border: none;
            cursor: pointer;
            padding: 0;
        }

        .melo-hamburger-menu span {
            background-color: var(--c700);
            display: block;
            width: 30px;
            height: 3px;
            margin: 6px 0;
            transition: 0.3s;
            position: relative;
        }

        /* X icon styles when menu is open */
        .melo-hamburger-menu.open span:nth-child(1) {
            transform: rotate(45deg) translate(6px, 6px);
        }

        .melo-hamburger-menu.open span:nth-child(2) {
            opacity: 0;
        }

        .melo-hamburger-menu.open span:nth-child(3) {
            transform: rotate(-45deg) translate(6px, -6px);
        }

        #melo-mobile-menu-overlay {
            position: fixed;
            display: none;
            height: 100%;
            width: 100vw;
            background-color: var(--c300);
            z-index: 0;
            opacity: 0;
            transition: opacity 0.4s ease-in-out;
        }

        #melo-mobile-menu-overlay.show-menu {
            display: block;
        }

        .melo-mobile-menu {
            height: calc(100lvh - 64px);
            padding: 32px;
            position: fixed;
            left: 0;
            width: 360px;
            z-index: 11;
            display: none;
            transform: translateX(-100%);
            transition: transform 0.3s ease-in-out;
            overflow: scroll;
            box-shadow: var(--meloShadow);
            background-color: var(--c100);
        }

        .melo-mobile-menu ul {
            width: 100%;
        }

        .melo-mobile-menu ul li {
            list-style: none;
            text-decoration: none;
            font-family: var(--fontMontserrat);
            font-size: 1rem;
            padding: 1.5rem 1rem;
            border-bottom: 1px solid var(--c300);
        }

        .melo-mobile-menu.show-menu {
            display: flex;
        }

        .melo-mobile-menu.slide-menu {
            transform: translateX(0);
        }

        .melo-mobile-menu ul li a,
        .melo-mobile-menu ul li a:visited {
            color: var(--c700);
            text-decoration: none;
            padding: 0;
            display: block;
        }

        .melo-mobile-menu li:hover {
            background-color: var(--c200);
            transition: ease 0.3s;
            cursor: pointer;
        }

        .melo-mobile-menu ul li a.melo-two-line-ellipsis {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical !important;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 100%;
        }

        #logoAnchor {
            height: 100%;
            width: auto;
            flex-grow: 0;
            overflow: hidden;
            transition: transform 0.3s ease-in;
            margin: auto;

            /* OPTIONAL START */
            /* Optional styles for non-transparent or sticker-like logos */

            /*
            display: flex;
            border-radius: 4px;
            padding: 4px 8px;
            width: 196px;
            box-shadow: var(--meloShadow);
            background-color: var(--logoBackgroundColor);
            justify-content: center;
            align-items: center;
            */

            /* OPTIONAL END */
        }

        #logoAnchor:hover {
            transform: scale(1.04);
        }

        img#logo {
            width: 216px;
            /* Fixed width to prevent shrinking */
            height: 48px;
            /* Fixed height to match the aspect ratio */
            min-width: 216px;
            /* Ensure logo doesn't shrink below this width */
            max-width: none;
            /* Override max-width constraint */
            object-fit: contain;
            /* Ensure the image maintains its aspect ratio within the fixed dimensions */
        }

        a#logoAnchor {
            min-width: 216px;
            flex-shrink: 0;
            /* Prevent the anchor from shrinking */
        }

        .melo-header-nav ul {
            display: flex;
            flex-direction: row;
            flex-grow: 1;
            /* Allow the ul to grow */
            flex-shrink: 1;
            /* Allow the ul to shrink */
            justify-content: flex-end;
            margin: auto 8px auto auto;
            column-gap: 4px;
            height: 100%;
            width: auto;
            overflow: hidden;
            /* Hide overflowing items */
        }

        .melo-header-nav ul li {
            display: block;
            list-style: none;
            cursor: pointer;
            transition: ease 0.3s;
            text-align: center;
            padding: 4px 16px;
            border-radius: 4px;
            /* change max-width to percentage points 100 divided by number of menu items */
            max-width: 33%;
            height: 48px;
            align-items: center;
        }

        .melo-header-nav li.melo-header-nav-highlighted-link {
            background-color: var(--c050);
            border-bottom: 3px solid var(--c200);
        }

        .melo-header-nav ul li:hover {
            border-bottom: 3px solid var(--projectAccent);
            transform: scaleX(1.02);
            cursor: pointer;
        }

        .melo-header-nav ul li a,
        .melo-header-nav ul li a:visited {
            color: var(--c700);
            text-decoration: none;
            display: block;
            padding: 20px 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .melo-dark-mode-div-wrapper {
            margin: auto 0 auto 8px;
            height: 100%;
            width: auto;
        }

        .melo-dark-mode-div {
            font-size: 8px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin: auto;
            height: 100%;
            width: auto;
        }

        .melo-dark-mode-div-mobile {
            font-size: 12px;
            overflow: hidden;
            display: flex;
            flex-direction: row;
            column-gap: 8px;
            justify-content: center;
            align-items: center;
            margin: auto;
            height: 100%;
            width: auto;
        }

        .melo-dark-mode-div p {
            line-height: 0;
            margin: 8px 0 0 0;
            text-wrap: nowrap;
        }

        .dark-mode-toggle-switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 18px;
            transition: transform 0.3s ease-in;
        }

        .dark-mode-toggle-switch:hover {
            transform: scale(1.1);
        }

        .dark-mode-toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .-dark-mode-toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--c300);
            transition: .4s;
        }

        .-dark-mode-toggle-slider:before {
            position: absolute;
            content: "";
            height: 10px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: #ffffff;
            transition: .4s;
            border-radius: 4px;
        }

        input+.-dark-mode-toggle-slider {
            border-radius: 4px;
        }

        input:checked+.-dark-mode-toggle-slider:before {
            transform: translateX(16px);
        }

        .melo-share-icon {
            /* Share icon visibility is controlled by JavaScript based on Web Share API availability */
            display: none;
            /* Initially hidden, will be shown via JavaScript if Web Share API is available */
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .melo-share-icon:hover {
            transform: scale(1.1);
        }

        #dark-mode-li {
            display: none;
        }

        @container (max-width: 960px) {
            .melo-header-nav ul {
                display: none;
            }

            .melo-hamburger-menu-wrapper {
                display: flex;
            }

            .melo-dark-mode-div-wrapper {
                display: none;
            }

            #dark-mode-li {
                display: flex;
            }
        }

        @container (max-width: 500px) {
            .melo-mobile-menu {
                width: 100%;
            }
        }

        /* Navigation Menu END */

        /* Footer START */

        footer {
            background-color: var(--c100);
            color: var(--c700);
            font-family: var(--fontMontserrat);
            font-size: 0.9rem;
            line-height: 2.2;
            text-align: center;
        }

        footer a {
            text-decoration: none;
            color: var(--c800);
            font-weight: 600;
            transition: ease 0.3s;
        }

        footer a:hover {
            border-bottom: 3px solid var(--projectAccent);
            cursor: pointer;
        }

        .melo-footer-wrapper {
            -webkit-column-count: 3;
            -moz-column-count: 3;
            column-count: 3;
            padding: 60px 20px 20px 20px;
            column-gap: 30px;
        }

        .melo-footer-bottom-centered {
            font-size: 0.8rem;
            margin: auto;
            padding: 20px 0 60px;
            width: 500px;
            text-align: center;
        }

        @container (max-width: 900px) {
            .melo-footer-wrapper {
                -webkit-column-count: 3;
                -moz-column-count: 3;
                column-count: 3;
            }
        }

        @container (max-width: 761px) {
            .melo-footer-wrapper {
                -webkit-column-count: 2;
                -moz-column-count: 2;
                column-count: 2;
            }
        }

        @container (max-width: 480px) {
            .melo-footer-wrapper {
                -webkit-column-count: 1;
                -moz-column-count: 1;
                column-count: 1;
            }
        }

        /* Footer END */

        /* ---------------------- MELO BLOCK STYLES ---------------------- */

        /* melo-columns */
        .melo-columns {
            display: flex;
            flex-direction: row;
        }

        .melo-column {
            width: 100%;
            display: flex;
            flex-direction: column;
        }

        @container (max-width: 700px) {
            .melo-columns {
                flex-direction: column;
            }

            .melo-column {
                min-width: 100%;
                max-width: 100%;
            }
        }

        /* melo-hero-header */
        .melo-hero-header {
            background-attachment: fixed;
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
        }

        .melo-hero-header-overlay {
            container-type: inline-size;
        }

        .melo-hero-header-banner {
            font-family: Verdana, Geneva, Tahoma, sans-serif;
            font-size: clamp(8px, 3cqw, 16px);
            border-radius: 5px;
            padding: 2px 18px;
            box-shadow: 1px 1px 8px #80808090;
            margin-bottom: 24px;
            line-height: 1.8;
            width: fit-content;
            max-width: 90%;
        }

        /* Combined text styles for better maintainability */
        .melo-hero-header-main-text,
        .melo-hero-header-secondary-text {
            font-family: var(--fontMontserrat);
            text-shadow: 1px 1px 8px #80808090;
        }

        .melo-hero-header-main-text {
            font-size: clamp(28px, 9cqw, 76px);
            line-height: 1.1;
            margin-bottom: 10px;
        }

        .melo-hero-header-secondary-text {
            font-size: clamp(14px, 4.5cqw, 38px);
            margin-bottom: 18px;
            line-height: 1.2;
        }

        .melo-hero-header a {
            font-size: clamp(12px, 4cqw, 24px);
            line-height: 1.8rem;
            font-family: Verdana, Geneva, Tahoma, sans-serif;
            font-weight: bold;
            padding: 0.6em 1.2em;
            box-shadow: 1px 1px 8px #80808090;
            border-radius: 8px;
            margin-top: 24px;
            text-decoration: none;
            transition: ease 0.3s;
            border: none;
            max-width: 90%;
        }

        .melo-hero-header a:hover {
            transform: scale(1.05);
            cursor: pointer;
        }

        /* melo-dates */
        .melo-dates-inner {
            font-family: "Courier New", Courier, "Lucida Console", Monaco, monospace;
            background-color: var(--softBackground);
            border-left: 3px solid;
            padding: 12px 20px;
            border-radius: 0 4px 4px 0;
            letter-spacing: -0.5px;
        }

        .melo-dates-inner p {
            margin: 0;
            line-height: 1.5;
            font-size: 0.9rem;
        }

        .melo-dates-inner time,
        .melo-dates-inner .author {
            font-weight: 950;
        }

        .melo-dates-inner .author a {
            color: var(--c600);
            text-decoration: none;
            transition: ease 0.3s;
        }

        .melo-dates-inner .author a:hover {
            border-bottom: 3px solid var(--projectAccent);
        }

        /* melo-toc */
        .melo-toc {
            font-family: "Courier New", Courier, "Lucida Console", Monaco, monospace;
            background-color: var(--softBackground);
            border-left: 3px solid;
            padding: 12px 20px;
            border-radius: 0 4px 4px 0;
            letter-spacing: -0.5px;
            color: var(--c900);
        }

        .melo-toc ul {
            list-style-type: none;
            margin: 0;
            padding: 0;
        }

        .melo-toc ul ul {
            padding-left: 20px;
            padding-top: 3px;
            padding-bottom: 3px;
        }

        .melo-toc li {
            margin: 0;
            line-height: 1.3;
        }

        .melo-toc a,
        .melo-toc a:visited,
        .melo-toc a:active {
            color: var(--c900);
            text-decoration: none;
            transition: ease 0.3s;
            font-size: 0.9rem;
        }

        /* Style section headers */
        .melo-toc .section-header {
            font-weight: 950;
            margin: 0;
            font-size: 0.9rem;
            color: var(--c600);
        }

        .melo-toc a:hover {
            border-bottom: 3px solid var(--projectAccent);
        }

        /* melo-breadcrumb */
        .melo-breadcrumb {
            font-family: var(--fontMontserrat);
            padding: 12px 0;
            color: var(--c700);
            font-size: 0.8rem;
        }

        .melo-breadcrumb ul {
            list-style-type: none;
            margin: 0;
            padding: 0;
            display: flex;
            flex-wrap: wrap;
        }

        .melo-breadcrumb ul li {
            display: inline-block;
            margin: 0;
            line-height: 1.5;
        }

        .melo-breadcrumb ul li:not(:last-child)::after {
            content: "/";
            margin: 0 8px;
            color: var(--c400);
        }

        .melo-breadcrumb a,
        .melo-breadcrumb a:visited,
        .melo-breadcrumb a:active {
            color: var(--c800);
            text-decoration: none;
            transition: ease 0.3s;
            font-weight: 600;
        }

        .melo-breadcrumb a:hover {
            border-bottom: 3px solid var(--projectAccent);
        }

        .melo-breadcrumb span {
            color: var(--c500);
        }

        /* melo-yt-video */

        .melo-yt-video {
            overflow: hidden;
            aspect-ratio: 16/9;
            transition: opacity 0.4s ease;
        }

        .melo-yt-video-container {
            position: relative;
            container-type: inline-size;
            width: 100%;
            height: 100%;
            background-size: 100% 100%;
        }

        .melo-yt-video-play-button {
            height: 12cqw;
            width: 12cqw;
            left: 50%;
            top: 50%;
            margin-left: -6cqw;
            margin-top: -6cqw;
            position: absolute;
        }

        .melo-yt-video-hoverable:not(.melo-yt-video-hoverable img):hover {
            -webkit-filter: brightness(85%);
            -moz-filter: brightness(85%);
            filter: brightness(85%);
        }

        .melo-yt-video-player {
            position: relative;
            padding-bottom: 56.25%;
            height: 0;
            overflow: hidden;
            max-width: 100%;
        }



        /* melo-text */
        .melo-container-wrapper[id^="melo-text"] {
            margin: 32px auto 32px auto;
        }

        .melo-text {
            font-family: var(--fontMontserrat);
            color: var(--c800);
            line-height: 1.7;
        }

        .melo-text h1,
        .melo-text h2,
        .melo-text h3,
        .melo-text h4,
        .melo-text h5,
        .melo-text h6 {
            margin-bottom: 1rem;
            margin-top: 2rem;
            line-height: 1.3;
            color: var(--c500);
            text-shadow: var(--meloShadow);
        }

        .melo-text h1 {
            font-size: 2.5rem;
        }

        .melo-text h2 {
            font-size: 2rem;
        }

        .melo-text h3 {
            font-size: 1.5rem;
        }

        .melo-text p {
            margin-bottom: 1rem;
        }

        .melo-text ul,
        .melo-text ol {
            margin: 1.6rem 0 1.6rem 2.5rem;
        }

        .melo-text li {
            line-height: 0.7;
        }

        .melo-text a,
        .melo-text a:visited,
        .melo-text a:active {
            color: var(--c800);
            text-decoration: underline;
            text-decoration-color: var(--projectAccent);
            text-decoration-thickness: 2px;
            text-underline-offset: 4px;
            transition: ease 0.3s;
            font-weight: 600;
        }

        .melo-text a:hover {
            background-color: var(--c100);
        }

        .melo-text blockquote {
            background-color: var(--softBackground);
            border-left: 3px solid var(--projectAccent);
            padding: 16px;
            border-radius: 0 4px 4px 0;
            margin: 1rem 0;
            font-size: 1.2rem;
            line-height: 1.5;
        }

        .melo-text blockquote p {
            margin: 0;
            color: var(--c500);
        }

        .melo-text pre {
            background-color: var(--c200);
            padding: 16px;
            border-radius: 4px;
            overflow-x: auto;
            margin: 1rem 0;
        }

        .melo-text code {
            font-family: "Courier New", Courier, "Lucida Console", Monaco, monospace;
            font-size: 0.9rem;
        }

        .melo-text strong {
            color: var(--c900);
        }

        /* Text alignment classes */
        .melo-text-align-center {
            text-align: center;
        }

        .melo-text-align-right {
            text-align: right;
        }

        .melo-text-align-justify {
            text-align: justify;
        }

        /* container queries */
        @container (max-width: 991px) {
            .melo-hero-header-banner {
                padding: 0.2em 1em;
                margin-bottom: 2.7cqw;
            }

            .melo-hero-header a {
                margin-top: 2.2cqw;
            }
        }

        @container (max-width: 767px) {
            .melo-hero-header-banner {
                padding: 0.3em 1em;
            }
        }

        /* Progressive enhancement for iOS/macOS devices that don't support fixed backgrounds well */
        @supports (-webkit-touch-callout: none) {

            /* iOS/Safari specific CSS */
            .melo-hero-header {
                background-attachment: scroll !important;
                -webkit-background-size: cover !important;
                background-size: cover !important;
            }
        }

        /* melo-button */
        .melo-button {
            display: inline-block;
            transition: ease 0.3s;
            font-family: var(--fontMontserrat);
            box-shadow: var(--meloShadow);
            text-decoration: none;
            cursor: pointer;
        }

        .melo-button:hover {
            transform: scale(1.06);
        }

        /* melo-testimonial */
        .melo-testimonial-slider {
            overflow: hidden;
        }

        .melo-testimonial-row {
            display: flex;
            flex-direction: row;
            container-type: inline-size;
        }

        .melo-testimonial {
            box-sizing: border-box;
            padding: 1.5rem 4rem;
            position: relative;
            font-style: italic;
            font-size: 1.1rem;
            font-family: Verdana, sans-serif;
            line-height: 1.7rem;
            display: flex;
            align-items: center;
            column-gap: 2rem;
            row-gap: 1rem;
            min-width: 100%;
            justify-content: center;
            container-type: inline-size;
        }

        .melo-testimonial-picture {
            height: 6rem;
            min-width: 6rem;
            border-radius: 50%;
            box-shadow: 0 2px 4px #00000033;
            aspect-ratio: 1/1;
            object-fit: cover;
        }

        .melo-testimonial-name {
            text-align: right;
            font-weight: bold;
            margin: 0.6rem 0 0.8rem;
        }

        @container (max-width: 761px) {
            .melo-testimonial-picture {
                height: 7.2rem;
                min-width: 7.2rem;
            }
        }

        @container (max-width: 500px) {
            .melo-testimonial {
                flex-direction: column;
                padding: 2.5rem;
            }
        }

        /* melo-image */
        .melo-image-wrapper {
            width: 100%;
        }

        .melo-image-wrapper figure {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
        }

        .melo-image-wrapper figure figcaption {
            font-family: var(--fontMontserrat);
            font-style: italic;
            font-size: 0.8rem;
            line-height: 2rem;
            width: 100%;
        }

        .melo-image {
            vertical-align: middle;
            height: auto;
            max-width: 100%;
        }

        /* ---------------------- MELO BLOCK STYLES SPECIFIC ---------------------- */

        /* melo-image-1234 */
        #melo-image-wrapper-1234 {
            margin: 40px auto 40px auto;
        }

        #melo-image-wrapper-1234 figure figcaption {
            text-align: left;
            width: 400px;
            /* 100% for full width */
        }

        #melo-image-1234 {
            box-shadow: var(--meloShadow);
            border-radius: 12px;
            width: 400px;
            /* 100% for full width */
        }

        @container (max-width:700px) {
            #melo-image-wrapper-1234 {
                max-width: 100cqw;
                border-radius: 0;

            }

            #melo-image-1234 {
                width: 100%;
                border-radius: 0;
            }

            #melo-image-wrapper-1234 figure figcaption {
                text-align: center;
                width: 100%;
            }
        }

        /* melo-testimonial-1234 */
        .melo-testimonial-slider-1234 {
            margin: 2rem auto;
            box-shadow: 0px 0px 8px 1px #d3d5d775;
            border-radius: 16px;
        }

        @keyframes melo-testimonial-slide-1234 {
            0% {
                transform: translateX(0);
            }

            16.67% {
                transform: translateX(0%);
            }

            33.33% {
                transform: translateX(-100%);
            }

            50% {
                transform: translateX(-100%);
            }

            66.67% {
                transform: translateX(-200%);
            }

            83.33% {
                transform: translateX(-200%);
            }

            100% {
                transform: translateX(-300%);
            }
        }

        .melo-testimonial-slider-1234:hover .melo-testimonial-1234 {
            animation-play-state: paused;
        }

        .melo-testimonial-1234 {
            animation: 21s melo-testimonial-slide-1234 infinite;
        }

        .melo-testimonial-1234:nth-child(odd) {
            background-color: #ffffff;
            color: #333333
        }

        .melo-testimonial-1234:nth-child(even) {
            background-color: #ededed;
            color: #333333;
        }

        /* melo-breadcrumb-1234 */
        #melo-breadcrumb-1234 {
            margin: 0 auto;
            background-color: var(--softBackground);
            border-top: 1px solid var(--c050);
            border-bottom: 1px solid var(--c050);
        }

        #melo-breadcrumb-1234 .melo-breadcrumb {
            padding: 8px 0;
        }

        /* hero-header-12345 */
        #hero-header-12345 {
            position: relative;
            container-type: inline-size;
            text-align: center;
            margin-top: 0px;
            margin-bottom: 0px;
            /* non-AMP version only */
            background-image: url('https://effectivemusicpractice-1701768237300-fast.b-cdn.net/assets/melo-block/hero-header/guitar-1209318_1920-1702931812653-1024px.webp');
            background-image: -webkit-image-set(url('https://effectivemusicpractice-1701768237300-fast.b-cdn.net/assets/melo-block/hero-header/guitar-1209318_1920-1702931812653-1024px.webp') 1x, url('https://effectivemusicpractice-1701768237300-fast.b-cdn.net/assets/melo-block/hero-header/guitar-1209318_1920-1702931812653-2048px.webp') 2x);
            background-image: image-set(url('https://effectivemusicpractice-1701768237300-fast.b-cdn.net/assets/melo-block/hero-header/guitar-1209318_1920-1702931812653-1024px.webp') 1x, url('https://effectivemusicpractice-1701768237300-fast.b-cdn.net/assets/melo-block/hero-header/guitar-1209318_1920-1702931812653-2048px.webp') 2x);
            background-attachment: fixed;
        }

        #hero-header-12345 .melo-hero-header-overlay {
            background-color: #565459b6;
            padding: 11cqw 0 9.5cqw 0;
        }

        #hero-header-12345 .melo-hero-header-banner {
            color: #ffffff;
            background-color: #f9a82f;
            border-radius: 5px;
            display: inline-block;
        }

        #hero-header-12345 .melo-hero-header-main-text {
            font-weight: 100;
            color: #ffffff;
            display: block;
        }

        #hero-header-12345 .melo-hero-header-secondary-text {
            font-weight: 500;
            color: #f9a82f;
            display: block;
        }

        #hero-header-12345 a {
            border-radius: 8px;
            border: 1px solid #ff0000;
            color: #ffffff;
            background-color: #fbaeae7a;
            display: inline-block;
        }

        /* melo-dates-12345 */
        #melo-dates-12345 {
            margin: 32px auto 16px auto;
        }

        #melo-dates-12345 .melo-dates-inner {
            border-left-color: #f9a82f;
        }

        #melo-dates-12345 .melo-dates-inner time,
        #melo-dates-12345 .melo-dates-inner .author {
            color: #f9a82f;
        }

        /* melo-toc-1234 */
        #melo-toc-1234 {
            margin: 16px auto 16px auto;
        }

        #melo-toc-1234 .melo-toc {
            border-left-color: #f9a82f;
        }

        /* melo-columns-1234 */
        #melo-columns-1234 {
            margin: 8px auto 32px auto;
            column-gap: 8px;
            row-gap: 8px;
        }

        #melo-column-1234-1 {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            max-width: 25%;
            background-color: var(--c050);
            padding: 16px;
            border: 1px solid #f9a82f;
            border-radius: 8px;
        }

        #melo-column-1234-2 {
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: center;
            max-width: 25%;
            background-color: var(--c050);
            padding: 16px;
            border: none;
            border-radius: 0px;
        }

        #melo-column-1234-3 {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            max-width: 50%;
            background-color: none;
            padding: 16px;
            border: none;
            border-radius: 0px;
        }


        /* melo-hr-1234 */

        #melo-hr-1234 hr {
            border-color: #e74343;
            width: 480px;
            margin: 48px auto 48px auto;
            box-shadow: var(--meloShadow);
        }

        /* melo-text-12345 */
        #melo-text-12345 {
            margin: 0px auto 32px auto;
        }

        /* melo-yt-video-123 */
        #melo-yt-video-123 {
            margin: 20px auto 40px auto;
            box-shadow: var(--meloShadow);
            border-radius: 12px;
            width: 900px;
        }

        #melo-yt-video-container-123 {
            background-image: url("https://i.ytimg.com/vi/2RpKmeedA4s/maxresdefault.jpg");
        }

        /* melo-button-12345 */
        #melo-button-12345 {
            text-align: center;
        }

        #melo-button-12345 a,
        button {
            padding: 1rem 2rem;
            font-size: 1.2rem;
            border-radius: 8px;
            border: 1px solid red;
            background-color: #fbaeae;
            box-shadow: var(--meloShadow);
            margin: 20px auto 20px auto;
            color: #ffffff;
            max-width: 50%;
            font-weight: bold;
        }
    </style>

</head>

<body>
    <header class="melo" itemscope itemtype="https://schema.org/Organization">

        <!-- melo-header-nav -->
        <div id="melo-mobile-menu-overlay"></div>
        <div class="melo melo-container-wrapper melo-header-nav-wrapper">
            <nav id="melo-header-nav-1234" class="melo melo-header-nav" aria-label="Main Menu">
                <div class="melo-hamburger-menu-wrapper">
                    <button class="melo-hamburger-menu" aria-expanded="false" aria-controls="melo-mobile-menu"
                        aria-label="Toggle navigation menu">
                        <span aria-hidden="true"></span>
                        <span aria-hidden="true"></span>
                        <span aria-hidden="true"></span>
                    </button>
                </div>
                <a id="logoAnchor" href="/" aria-label="Home">
                    <img alt="Guitar Learning Co." itemprop="name" id="logo"
                        src="https://effectivemusicpractice.b-cdn.net/assets/logo/emp-logo-984px.webp" srcset="https://effectivemusicpractice.b-cdn.net/assets/logo/emp-logo-984px.webp 1x,
                            https://effectivemusicpractice.b-cdn.net/assets/logo/emp-logo-984px-2x.webp 2x"
                        alt="Project Name Logo" width="216" height="48">
                </a>
                <ul>
                    <li class="melo-header-nav-highlighted-link">
                        <a href="/test-page">45 String Fragment System</a>
                    </li>
                    <li>
                        <a href="https://string.systems/">String Systems Website</a>
                    </li>
                    <li>
                        <a href="https://string.systems/about">About/Contact</a>
                    </li>
                </ul>

                <svg class="melo-share-icon" id="melo-share-icon-ios" width="48px" height="48px" viewBox="0 0 24 24"
                    style="padding: 8px;">
                    <g stroke="none" stroke-width="1" fill-rule="evenodd">
                        <g fill="var(--c700)" fill-rule="nonzero">
                            <path d="M0 0h24v24H0V0z" fill="none" />
                            <path
                                d="M16 5l-1.42 1.42-1.59-1.59V16h-1.98V4.83L9.42 6.42 8 5l4-4 4 4zm4 5v11c0 1.1-.9 2-2 2H6c-1.11 0-2-.9-2-2V10c0-1.11.89-2 2-2h3v2H6v11h12V10h-3V8h3c1.1 0 2 .89 2 2z">
                            </path>
                        </g>
                    </g>
                </svg>
                <div class="melo-dark-mode-div-wrapper">
                    <div class="melo-dark-mode-div">
                        <label class="dark-mode-toggle-switch">
                            <input id="dark-mode-switch" type="checkbox" aria-labelledby="dark-mode-label">
                            <span class="-dark-mode-toggle-slider"></span>
                        </label>
                        <p id="dark-mode-label">DARK MODE</p>
                    </div>
                </div>
            </nav>
            <nav class="melo-mobile-menu melo-scroll-container" id="melo-mobile-menu" aria-label="Main Menu">
                <ul>
                    <li><a href="/lessons/sfs-pentatonics/" class="melo-two-line-ellipsis">String Fragment System String
                            Fragment
                            System blah blah
                            isgdiushiushiudshi</a></li>
                    <li><a href="https://string.systems/" class="melo-two-line-ellipsis">String Systems Website</a></li>
                    <li><a href="https://string.systems/about" class="melo-two-line-ellipsis">About/Contact</a></li>
                    <li id="dark-mode-li">
                        <div class="melo-dark-mode-div-mobile">
                            <label class="dark-mode-toggle-switch">
                                <input id="dark-mode-switch-mobile" type="checkbox"
                                    aria-labelledby="dark-mode-label-mob">
                                <span class="-dark-mode-toggle-slider"></span>
                            </label>
                            <p id="dark-mode-label-mob">DARK MODE</p>
                        </div>
                    </li>
                </ul>
            </nav>
        </div>

    </header>

    <main itemscope itemtype="https://schema.org/HowTo">

        <!-- melo-utility-section-opening-tag START -->
        <section id="melo-utility-section-opening-tag-1234">
            <!-- melo-utility-section-opening-tag END -->

            <!-- hero-header -->
            <div id="hero-header-12345" class="melo melo-hero-header">
                <div class="melo-hero-header-overlay">
                    <p class="melo-hero-header-banner melo-width-boxed">
                        UPGRADE YOUR CHORD SKILLS
                    </p>
                    <h1 itemprop="name" class="melo-hero-header-main-text melo-width-boxed">
                        Barre Ελληνικά Workshop</h1>
                    <p itemprop="description" class="melo-hero-header-secondary-text melo-width-boxed">UNDERSTANDING
                        MOVEABLE CHORDS</p>
                    <a href="https://w3docs.com" target="_top">Call To Action</a>
                </div>
            </div>

            <!-- melo-utility-section-closing-tag START -->
        </section>
        <!-- melo-utility-section-closing-tag END -->

        <!-- melo-breadcrumb START -->
        <div class="melo melo-container-wrapper" id="melo-breadcrumb-1234">
            <div class="melo-breadcrumb melo-width-boxed">
                <ul>
                    <li><a href="/">justplay.systems</a></li>
                    <li><a href="/guitar-chords">Chords & Rhythm Guitar</a></li>
                    <li><span>Barre Chord Lesson</span></li>
                </ul>
            </div>
        </div>
        <!-- melo-breadcrumb END -->

        <!-- melo-dates START -->
        <div id="melo-dates-12345" class="melo melo-container-wrapper">
            <div class="melo-dates-inner melo-width-boxed">
                <p>Published on: <time itemprop="datePublished" datetime="2023-11-16">November 16, 2023</time></p>
                <p>Last updated on: <time itemprop="dateModified" datetime="2025-03-07">March 7, 2025</time></p>
                <p>Author: <span class="author" itemprop="author" itemscope itemtype="https://schema.org/Person"><a
                            href="https://string.systems/about" itemprop="url"><span itemprop="name">Prokopis
                                Skordis</span></a></span></p>
            </div>
        </div>
        <!-- melo-dates END -->

        <!-- melo-toc START -->
        <div id="melo-toc-1234" class="melo melo-container-wrapper">
            <nav class="melo-toc melo-width-boxed">
                <ul>
                    <li>
                        <p class="section-header">Section 1</p>
                        <ul>
                            <li>
                                <a href="#link1">Link 1</a>
                            </li>
                            <li>
                                <a href="#link2">Link 2</a>
                            </li>
                            <li>
                                <a href="#link3">Link 3</a>
                            </li>
                            <li>
                                <a href="#link4">Link 4</a>
                            </li>
                        </ul>
                    </li>
                    <li><a class="section-header" href="#section2">Section 2</a>
                        <ul>
                            <li>
                                <a href="#link5">Link 5</a>
                            </li>
                            <li>
                                <a href="#link6">Link 6</a>
                            </li>
                            <li>
                                <a href="#link7">Link 7</a>
                            </li>
                            <li>
                                <a href="#link8">Link 8</a>
                            </li>
                        </ul>
                    </li>
                    <li><a class="section-header" href="#lonelink1">Lonely Link 1</a></li>
                    <li><a class="section-header" href="#lonelink2">Lonely Link 2</a></li>
                </ul>
            </nav>
        </div>
        <!-- melo-toc END -->

        <!-- melo-hr START-->
        <div class="melo melo-container-wrapper" id="melo-hr-1234">
            <hr class="melo melo-width-boxed">
        </div>
        <!-- melo-hr END-->

        <!-- melo-utility-section-opening-tag START -->
        <section id="melo-utility-section-opening-tag-1235" itemprop="step" itemscope
            itemtype="http://schema.org/HowToStep">
            <!-- melo-utility-section-opening-tag END -->

            <!-- melo-text START -->
            <div class="melo-container-wrapper" id="melo-text-12345">
                <div class="melo melo-text melo-width-boxed">
                    <h1>This is an H1 title</h1>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut
                        labore
                        et
                        dolore magna
                        aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
                        commodo
                        consequat.
                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
                        pariatur.
                        Excepteur
                        sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est
                        laborum.
                    </p>
                    <h2>This is an H2 title</h2>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut
                        labore
                        et
                        dolore magna
                        aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
                        commodo
                        consequat.
                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
                        pariatur.
                        Excepteur
                        sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est
                        laborum.
                    </p>
                    <h3>This is an H3 Title</h3>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut
                        labore
                        et
                        dolore magna
                        aliqua. <a target="_blank" rel="noopener noreferrer" href="https://string.systems">Ut enim ad
                            minim
                            veniam</a>, quis
                        nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure
                        dolor
                        in
                        reprehenderit
                        in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat
                        cupidatat
                        non
                        proident, sunt
                        in culpa qui officia deserunt mollit anim id est laborum.</p>
                    <ul>
                        <li>
                            <p>This is a bullet</p>
                        </li>
                        <li>
                            <p>And another</p>
                        </li>
                    </ul>
                    <ol>
                        <li>
                            <p>Item <em>number</em> one</p>
                        </li>
                        <li>
                            <p>Item number <strong>two</strong></p>
                        </li>
                    </ol>
                    <p></p>
                    <p class="melo-text-align-center">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
                        eiusmod
                        tempor
                        incididunt
                        ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco
                        laboris
                        nisi ut aliquip
                        ex ea commodo consequat. Duis aute irure dolor in reprehenderit in velit esse cillum dolore eu
                        fugiat
                        nulla
                        pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt
                        <s>mollit</s> anim id
                        est <u>laborum</u>.
                    </p>
                    <p></p>
                    <p class="melo-text-align-right">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
                        eiusmod
                        tempor
                        incididunt
                        ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco
                        laboris
                        nisi ut aliquip
                        ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum
                        dolore eu
                        fugiat
                        nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia
                        deserunt
                        mollit anim id
                        est laborum.</p>
                    <p class="melo-text-align-justify"></p>
                    <p class="melo-text-align-justify">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
                        eiusmod
                        tempor incididunt
                        ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco
                        laboris
                        nisi ut aliquip
                        ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum
                        dolore eu
                        fugiat
                        nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia
                        deserunt
                        mollit anim id
                        est laborum.</p>
                    <p></p>
                    <pre><code>This is some code</code></pre>
                    <blockquote>
                        <p><em>This is a quote nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in
                                culpa nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa</em>
                        </p>
                    </blockquote>
                    <p></p>
                    <h3>This is an H3 Title</h3>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut
                        labore
                        et
                        dolore magna
                        aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
                        commodo
                        consequat.
                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
                        pariatur.
                        Excepteur
                        sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est
                        laborum.
                    </p>
                </div>
            </div>
            <!-- melo-text END -->

            <!-- melo-utility-section-closing-tag START -->
        </section>
        <!-- melo-utility-section-closing-tag END -->

        <!-- melo-utility-article-opening-tag START -->
        <article id="melo-utility-article-opening-tag-1234" itemprop="step" itemscope
            itemtype="http://schema.org/HowToStep">
            <!-- melo-utility-article-opening-tag END -->

            <!-- Columns -->
            <div class="melo melo-container-wrapper">
                <div id="melo-columns-1234" class="melo melo-columns melo-width-boxed">
                    <div id="melo-column-1234-1" class="melo-column">
                        <div class="melo-text melo-width-boxed">
                            <h2>This is an H2 title</h2>
                            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt
                                ut
                                labore
                                et
                                dolore magna
                                aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut
                                aliquip ex ea
                                commodo
                                consequat.
                                Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat
                                nulla
                                pariatur.
                                Excepteur
                                sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id
                                est
                                laborum.
                            </p>
                        </div>
                    </div>
                    <div id="melo-column-1234-2" class="melo-column">
                        <div class="melo-text">
                            <p>second Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                                incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
                                exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure
                                dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
                                Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt
                                mollit anim id est laborum.</p>
                        </div>
                    </div>
                    <div id="melo-column-1234-3" class="melo-column">
                        <div class="melo-text">
                            <p>third Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                                incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
                                exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure
                                dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
                                Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt
                                mollit anim id est laborum.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- melo-utility-article-closing-tag START -->
        </article>
        <!-- melo-utility-article-closing-tag END -->

        <!-- melo-utility-section-opening-tag START -->
        <section id="melo-utility-section-opening-tag-1236" itemprop="step" itemscope
            itemtype="http://schema.org/HowToStep">
            <!-- melo-utility-section-opening-tag END -->

            <!-- melo-yt-video START -->
            <div class="melo melo-container-wrapper">
                <noscript>
                    <div id="melo-yt-video-123" class="melo-yt-video melo-width-boxed">
                        <div class="melo-yt-video-player">
                            <iframe src="https://www.youtube.com/embed/2RpKmeedA4s?rel=0" loading="lazy"
                                style="border: none; position: absolute; top: 0; height: 100%; width: 100%;"
                                allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;"
                                allowfullscreen="true"></iframe>
                        </div>
                    </div>
                </noscript>

                <div id="melo-yt-video-123" class="melo-yt-video melo-width-boxed melo-yt-video-hoverable">
                    <div id="melo-yt-video-container-123" class="melo-yt-video-container" onclick=loadYouTubeVideo123()
                        style="cursor: pointer;">
                        <img class="melo-yt-video-play-button" src="https://melo-assets.b-cdn.net/melo-play-button.png"
                            alt="Play Button">
                    </div>
                </div>
            </div>

            <script>
                function loadYouTubeVideo123() {
                    var placeholder = document.getElementById("melo-yt-video-container-123");
                    placeholder.innerHTML = `<div class="melo-yt-video-player"><iframe src="https://www.youtube.com/embed/2RpKmeedA4s?rel=0&autoplay=1" loading="lazy" style="border: none; position: absolute; top: 0; height: 100%; width: 100%;" allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;" allowfullscreen="true"></iframe></div>`;
                    document.getElementById("melo-yt-video-123").classList.remove('melo-yt-video-hoverable');
                }
            </script>

            <script type="application/ld+json">
                            {"@context":"https://schema.org","@type":"VideoObject","name":"Learn The Fretboard In 7 Steps - Fun and Effective System","description":"FREE - Fretboard Memorization Toolbox: https://guitarl.ink/fret\n\n\nLearn The Fretboard In 7 Steps\n---------------------------------------------------\nIn this lesson, we take a look at a fun and effective system for learning the notes on the guitar fretboard. In this method, instead of dry memorization, we use game-type drills, and creative improvisation, to help implant the notes into our memory.\n\n\n--- Contents Of This Video ---\n\n0:00 - Intro\n2:46 - Step 1 - ABCD Horizontal (By String)\n5:31 - Step 2 - ABCD Vertical (By Fretboard Area)\n6:56 - Step 3 - ABCDEFG Horizontal (By String)\n8:19 - Step 4 - ABCDEFG Vertical (By Fretboard Area)\n9:16 - Step 5 - ABCDEFG Free Improvisation\n11:07 - Steps 6 \u0026 7 - All Fretboard Notes\n13:02 - Extra: Root Note Location Practice\n\n\nThe Value Of Memorizing The Fretboard\n----------------------------------------------------------------\n    The guitar is one of the few instruments where knowing the actual notes you are playing is not a big priority for most people, at least not in the beginning. If you look at the piano, for example, you’ll see that little children that have been playing for just a few months, or weeks even, know all the notes. They can name any key on the keyboard. But take the average adult that has been playing guitar for over a decade, point to a random note on the fretboard, and chances are that he or she will not know the name of that note, or will need some time to figure it out 🤔.\n\n    That’s not because guitar players are dumb or musically illiterate, but because the geometric nature of the guitar fretboard causes us to prioritize other things over learning all the notes on the fretboard. When you learn a simple C major chord in the open position, for example, it’s much easier to just learn the fingering shape and place it on the fretboard, instead of thinking that you are playing \u0022C, E, G, C, E\u0022. And even if you are a seasoned “intellectual\u0022 player and know all the notes on fretboard well, and you want to play, for example, an Eb major 7 drop 2 voicing, you will most probably place it using the shape, and maybe the root note, instead of thinking \u0022Eb, Bb, D, G\u0022, even if you do know these notes.\n\n    The same goes for playing scales. Whether you like to memorize shapes or prefer to use SFS and form your fingerings on the fly, you still don’t need to think of the name of every note you play. But as you progress, and as your playing starts to become more sophisticated, knowing the notes on the fretboard becomes increasingly important.\n\n    The deeper you go with your guitar journey, the more you will see how learning the fretboard really well, and locating notes really fast, can help open up more and more musical possibilities.\n\n    Many people think that this is just too hard, and never try to do it. That’s because, indeed, the location of notes on the guitar fretboard is not as obvious as on the piano keyboard, or as on a saxophone, and so on. That doesn’t mean that we can’t learn them. We just need a step by step system to do it.\n\n    So when I started teaching in 2001, I saw the need for developing such a system, in order to help my students get to their destination sooner. That’s what I’m sharing with you here. I will explain the process, and then you can download, or stream the free “Fretboard Memorization Toolbox” which includes everything you need to practice this method. It also includes a PDF  where you can find all the diagrams and a clear list of all the steps so that you can follow them in your practice time.\n    This fun toolbox will help you learn the notes on the guitar fretboard, using creative drills and mini-games.\n\n    As you will see from the video lesson, memorizing the fretboard does NOT have to be boring. With Fretboard Memorization Toolbox it can feel like a fretboard memorization game. The mini-course package includes video drills, PDF documentation, and mp3 backing tracks, which you can download or stream online. There\u0027s also a guitar fretboard diagram for each step, but it\u0027s not supposed to be used for dry memorization. It\u0027s just a guide to help you work through the videos, and you will discover that after a few minutes you won\u0027t need it! Just have fun with the drills, and watch how locating the notes on the guitar fretboard will become second nature! \n\nThanks for watching, and remember:\nEnjoy your Practice and Be Effective!\n\nProkopis Skordis\nhttps://string.systems\n\n\u0022Efficiency is doing things right. Effectiveness is doing the right things.\u0022\n\n#guitarlesson #SFS #guitar #fretboard","thumbnailUrl":"https://i.ytimg.com/vi/2RpKmeedA4s/maxresdefault.jpg","uploadDate":"2016-09-13T07:10:34Z","duration":"PT13M45S","embedUrl":"https://www.youtube.com/embed/2RpKmeedA4s","interactionCount":"196675"}
                        </script>
            <!-- melo-yt-video END -->

            <!-- melo-utility-section-closing-tag START -->
        </section>
        <!-- melo-utility-section-closing-tag END -->

        <!-- melo-testimonial-slider START -->
        <div class="melo melo-container-wrapper" id="melo-testimonial-slider-1234">
            <div class="melo-testimonial-slider melo-testimonial-slider-1234 melo-width-boxed">
                <div class="melo-testimonial-row">
                    <div class="melo-testimonial melo-testimonial-1234"><img class="melo-testimonial-picture"
                            src="https://string-systems.b-cdn.net/assets/melo-block-testimonial-pics/1700065511270-henderson-suttle.jpg"
                            alt="Reviewer Picture">
                        <div>
                            <p><br>"Prokopis genuinely cares about his students. His teaching approach is one-of-a-kind,
                                and his encouragement and clarity have greatly contributed to my improvement. I am
                                grateful for
                                his guidance and support."<br></p>
                            <p class="melo-testimonial-name">Henderson Suttle</p>
                        </div>
                    </div>
                    <div class="melo-testimonial melo-testimonial-1234"><img class="melo-testimonial-picture"
                            src="https://string-systems.b-cdn.net/assets/melo-block-testimonial-pics/1700067511017-daniel-cooper.jpg"
                            alt="Reviewer Picture">
                        <div>
                            <p><br>"Prokopis’ skillful teaching has helped me make tremendous progress in a very short
                                time.
                                Much appreciated!"<br></p>
                            <p class="melo-testimonial-name">Daniel Cooper</p>
                        </div>
                    </div>
                    <div class="melo-testimonial melo-testimonial-1234"><img class="melo-testimonial-picture"
                            src="https://string-systems.b-cdn.net/assets/melo-block-testimonial-pics/1700068693228-George-Vetsos.jpg"
                            alt="Reviewer Picture">
                        <div>
                            <p><br>"As a guitar teacher myself, Prokopis' unique methods have not only helped me improve
                                my own
                                playing but have also influenced and shaped my own teaching style. I am truly grateful
                                for his
                                guidance and the impact he has had on my musical journey."<br></p>
                            <p class="melo-testimonial-name">George Vetsos</p>
                        </div>
                    </div>
                    <div class="melo-testimonial melo-testimonial-1234"><img class="melo-testimonial-picture"
                            src="https://string-systems.b-cdn.net/assets/melo-block-testimonial-pics/1700065511270-henderson-suttle.jpg"
                            alt="Reviewer Picture">
                        <div>
                            <p><br>"Prokopis genuinely cares about his students. His teaching approach is one-of-a-kind,
                                and his encouragement and clarity have greatly contributed to my improvement. I am
                                grateful for
                                his guidance and support."<br></p>
                            <p class="melo-testimonial-name">Henderson Suttle</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- melo-testimonial-slider END -->

        <!-- melo-image START -->
        <div class="melo melo-container-wrapper">
            <div id="melo-image-wrapper-1234" class="melo-image-wrapper melo-width-boxed">
                <figure>
                    <img id="melo-image-1234" class="melo-image"
                        src="https://melo-assets.b-cdn.net/955869c4-d131-4a02-b5dd-f356c5d57af4.jpeg"
                        srcset="https://melo-assets.b-cdn.net/955869c4-d131-4a02-b5dd-f356c5d57af4.jpeg 1x, https://melo-assets.b-cdn.net/955869c4-d131-4a02-b5dd-f356c5d57af4.jpeg 2x"
                        alt="This is an image of a guitar" width="1920" height="1080" loading="lazy">
                    <figcaption>Caption goes here</figcaption>
                </figure>
            </div>
        </div>
        <!-- melo-image END -->

        <!-- melo-button START -->
        <div class="melo melo-container-wrapper" id="melo-button-12345">
            <a class="melo-button" href="https://string.systems" target="_blank">press
                button Lorem ipsum dolor sit amet consectetur !</a>
        </div>
        <!-- melo-button END -->

    </main>
    <!-- Footer START -->
    <footer class="melo melo-container-wrapper ">
        <div class="melo-footer-wrapper melo-width-boxed">
            <p><a href="https://courses.string.systems/p/free-courses">Free Mini Courses</a></p>
            <p><a href="https://courses.string.systems/p/premium-courses">Premium Courses</a></p>
            <p><a href="https://courses.string.systems/p/pps">Access All Courses</a></p>
            <p><a href="https://string.systems/blog">String Systems Blog</a></p>
            <p><a href="https://string.systems/private-lessons">Online Private Lessons</a></p>
            <p><a href="https://urlgeni.us/youtube/channel/string">YouTube Channel</a></p>
            <p><a href="https://string.systems/app">String Courses App</a></p>
            <p><a href="https://help.string.systems/">Help Center</a></p>
            <p><a href="https://courses.string.systems/sign_in">Log in to my courses</a></p>
        </div>
        <div class="melo-footer-bottom-centered melo-width-boxed">
            <hr>
            <br>
            <p>Copyright &copy; 2017-2025 Prokopis Skordis</p>
            <p><a href="https://string.systems/about">About</a> / <a
                    href="https://string.systems/privacy-policy">Privacy
                    Policy</a> / <a href="https://string.systems/terms">Terms Of Use</a></p>
            <p>String Systems aka EffectiveMusicPractice LTD</p>
            <p>Nikolaou Skoufa 29, Limassol 3016, Cyprus.</p>
            <p>email: <EMAIL></p>
        </div>
    </footer>
    <!-- Footer END -->

    <!-- SCRIPTS START -->
    <script>
        /* SCRIPT: Get the dark mode switch element */

        const darkModeSwitch = document.getElementById('dark-mode-switch');
        const darkModeSwitchMob = document.getElementById('dark-mode-switch-mobile');

        /* SCRIPT: Function to set the dark mode value in localStorage + toggle color cariables and logo. */

        function setDarkMode(value) {
            localStorage.setItem('darkMode', value);

            if (value === true) {
                document.documentElement.style.setProperty('--c000', '#121212');
                document.documentElement.style.setProperty('--c050', '#2D2D2D');
                document.documentElement.style.setProperty('--c100', '#404040');
                document.documentElement.style.setProperty('--c200', '#545454');
                document.documentElement.style.setProperty('--c300', '#6C6C6C');
                document.documentElement.style.setProperty('--c400', '#828282');
                document.documentElement.style.setProperty('--c500', '#9C9C9C');
                document.documentElement.style.setProperty('--c600', '#AAAAAA');
                document.documentElement.style.setProperty('--c700', '#C8C8C8');
                document.documentElement.style.setProperty('--c800', '#EBEBEB');
                document.documentElement.style.setProperty('--c900', '#F7F7F7');
                document.documentElement.style.setProperty('--c950', '#FFFFFF');
                document.documentElement.style.setProperty('--softBackground', '#2d2d2d2c');
                document.documentElement.style.setProperty('--whiteBlack', '#000');
                document.documentElement.style.setProperty('--projectAccent', '#f9a82f');
                document.documentElement.style.setProperty('--projectSecondary', '#ffffff');
                document.documentElement.style.setProperty('--transparentHeaderNav', '#242424ed');
                document.documentElement.style.setProperty('--meloShadow', '0px 0px 10px #202020');
                /* Optional styles for non-transparent or sticker-like logos */
                /* user specifies logo background color */
                /*document.documentElement.style.setProperty('--logoBackgroundColor', '#000000');*/
                document.getElementById('logo').setAttribute('src', 'https://effectivemusicpractice.b-cdn.net/assets/logo/emp-logo-984px-dm.webp');
                document.getElementById('logo').setAttribute('srcset', 'https://effectivemusicpractice.b-cdn.net/assets/logo/emp-logo-984px-dm.webp 1x, https://effectivemusicpractice.b-cdn.net/assets/logo/emp-logo-984px-dm-2x.webp 2x');
                darkModeSwitch.checked = true;
                darkModeSwitchMob.checked = true;
            } else {
                document.documentElement.style.setProperty('--c000', '#FFFFFF');
                document.documentElement.style.setProperty('--c050', '#F7F7F7');
                document.documentElement.style.setProperty('--c100', '#EBEBEB');
                document.documentElement.style.setProperty('--c200', '#C8C8C8');
                document.documentElement.style.setProperty('--c300', '#AAAAAA');
                document.documentElement.style.setProperty('--c400', '#9C9C9C');
                document.documentElement.style.setProperty('--c500', '#828282');
                document.documentElement.style.setProperty('--c600', '#6C6C6C');
                document.documentElement.style.setProperty('--c700', '#545454');
                document.documentElement.style.setProperty('--c800', '#404040');
                document.documentElement.style.setProperty('--c900', '#2D2D2D');
                document.documentElement.style.setProperty('--c950', '#121212');
                document.documentElement.style.setProperty('--softBackground', '#f7f7f7');
                document.documentElement.style.setProperty('--whiteBlack', '#fff');
                document.documentElement.style.setProperty('--projectAccent', '#f9a82f');
                document.documentElement.style.setProperty('--projectSecondary', '#524d88');
                document.documentElement.style.setProperty('--transparentHeaderNav', '#ffffffed');
                document.documentElement.style.setProperty('--meloShadow', '0px 0px 10px #d8d8d8');
                /* Optional styles for non-transparent or sticker-like logos */
                /* user specifies logo background color */
                /*document.documentElement.style.setProperty('--logoBackgroundColor', '#ffffff');*/
                document.getElementById('logo').setAttribute('src', 'https://effectivemusicpractice.b-cdn.net/assets/logo/emp-logo-984px.webp');
                document.getElementById('logo').setAttribute('srcset', 'https://effectivemusicpractice.b-cdn.net/assets/logo/emp-logo-984px.webp 1x, https://effectivemusicpractice.b-cdn.net/assets/logo/emp-logo-984px-2x.webp 2x');
                darkModeSwitch.checked = false;
                darkModeSwitchMob.checked = false;
            }
        }

        /* SCRIPT: Retrieve the value of the 'dark-mode' URL parameter and set dark mode accordingly */

        if (window.location.search.includes('dark-mode=true')) { setDarkMode(true) }
        else {
            if (window.location.search.includes('dark-mode=false')) { setDarkMode(false) }
            else {
                // Check if darkMode is set in localStorage, if not (first visit) default to dark mode
                const storedDarkMode = localStorage.getItem('darkMode');
                if (storedDarkMode === null) {
                    setDarkMode(true); // Default to dark mode for first-time visitors
                } else {
                    setDarkMode(storedDarkMode === 'true');
                }
            }
        }

        /* SCRIPT: Add event listener to the dark mode switch and set dark mode accordingly */

        darkModeSwitch.addEventListener('change', function () {
            setDarkMode(this.checked);
        }, { passive: true });
        darkModeSwitchMob.addEventListener('change', function () {
            setDarkMode(this.checked);
        }, { passive: true });

        /* SCRIPT: Add event listener for storage changes and set dark mode accordingly */

        window.addEventListener('storage', function (event) {
            if (event.key === 'darkMode') {
                setDarkMode(localStorage.getItem('darkMode') === 'true')
            }
        }, { passive: true });

        /* SCRIPT: Check for share-api and show the iOS share icon. Handle the click if share-api is enabled */

        const shareCanonicalUrl = window.location.href;
        const shareTitle = document.title;
        const shareFaviconUrl = document.querySelector('link[rel="icon"]')?.href;
        const darkModeBlock = document.getElementsByClassName('melo-dark-mode-div-wrapper')[0];
        const darkModeBlockMob = document.getElementById("dark-mode-li");
        const navList = document.querySelector('.melo-header-nav ul');
        const hamburgerMenuWrapper = document.querySelector('.melo-hamburger-menu-wrapper');

        if (!navigator.share) {
            console.log('Web Share API is not supported in this browser');
        } else {
            // Always use the iOS share icon
            const shareIcon = document.getElementById('melo-share-icon-ios');

            // Only show share icon on mobile/tablet views
            const handleResize = () => {
                if (shareIcon) {
                    shareIcon.style.display = window.innerWidth <= 960 ? "block" : "none";
                }
            };

            // Initial check
            handleResize();

            // Listen for window resize
            window.addEventListener('resize', handleResize, { passive: true });

            // Add click handler
            if (shareIcon) {
                shareIcon.addEventListener('click', async () => {
                    try {
                        await navigator.share({
                            title: shareTitle,
                            text: shareTitle,
                            url: shareCanonicalUrl,
                            icon: shareFaviconUrl,
                        });
                        console.log('Shared successfully');
                    } catch (error) {
                        console.error('Error sharing:', error);
                    }
                }, { passive: true });
            }
        }

        /* SCRIPT function to toggle mobile menu */

        const hamburgerMenu = document.querySelector('.melo-hamburger-menu');
        const menuOverlay = document.getElementById('melo-mobile-menu-overlay');
        const mobileMenu = document.querySelector('.melo-mobile-menu');
        let menuStatus = false;
        function toggleMenu() {
            if (menuStatus === false) {
                menuStatus = true;
                menuOverlay.classList.toggle('show-menu');
                menuOverlay.style.setProperty('opacity', '0.7');
                mobileMenu.classList.toggle('show-menu');
                hamburgerMenu.setAttribute('aria-expanded', 'true');
                hamburgerMenu.classList.add('open'); // Add open class to transform to X
                document.body.classList.add('menu-open');
                setTimeout(function () {
                    mobileMenu.classList.toggle('slide-menu');
                }, 10);
            }
            else {
                menuStatus = false;
                mobileMenu.classList.toggle('slide-menu');
                menuOverlay.style.setProperty('opacity', '0');
                hamburgerMenu.setAttribute('aria-expanded', 'false');
                hamburgerMenu.classList.remove('open'); // Remove open class to revert to hamburger
                document.body.classList.remove('menu-open');
                setTimeout(function () {
                    mobileMenu.classList.toggle('show-menu');
                    menuOverlay.classList.toggle('show-menu');
                }, 300);
            }
        };

        /* SCRIPT EventListeners to animate hamburger and toggle mobile menu */

        hamburgerMenu.addEventListener('click', () => {
            toggleMenu()
        }, { passive: true });
        menuOverlay.addEventListener('click', () => {
            toggleMenu()
        }, { passive: true });
    </script>

</body>

</html>